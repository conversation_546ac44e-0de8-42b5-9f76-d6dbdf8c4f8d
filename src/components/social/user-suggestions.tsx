"use client"

import React, { useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { FollowButton } from './follow-button'
import { useFollowSystem } from '@/hooks/social/use-follow-system'
import { useSocialStore, selectSuggestedUsers } from '@/lib/stores/social-store'
import { Verified, Users, FileText } from 'lucide-react'
import Link from 'next/link'

interface UserSuggestionsProps {
  className?: string
  title?: string
  limit?: number
  showStats?: boolean
  variant?: 'card' | 'list' | 'grid'
}

export function UserSuggestions({
  className,
  title = "Suggested for you",
  limit = 5,
  showStats = true,
  variant = 'card',
}: UserSuggestionsProps) {
  const { loadSuggestedUsers, isLoadingSuggestions } = useFollowSystem()
  const suggestedUsers = useSocialStore(selectSuggestedUsers)

  useEffect(() => {
    if (suggestedUsers.length === 0) {
      loadSuggestedUsers()
    }
  }, [loadSuggestedUsers, suggestedUsers.length])

  const displayUsers = suggestedUsers.slice(0, limit)

  if (isLoadingSuggestions && suggestedUsers.length === 0) {
    return <UserSuggestionsSkeleton variant={variant} title={title} />
  }

  if (displayUsers.length === 0) {
    return null
  }

  const formatCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  if (variant === 'list') {
    return (
      <div className={cn('space-y-3', className)}>
        {title && (
          <h3 className="text-lg font-semibold">{title}</h3>
        )}
        <div className="space-y-2">
          {displayUsers.map((user) => (
            <div
              key={user.id}
              className="flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <Link href={`/profile/${user.username || user.id}`}>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user.avatarUrl} alt={user.displayName} />
                    <AvatarFallback>
                      {(user.displayName || user.username || 'U').charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Link>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1">
                    <Link 
                      href={`/profile/${user.username || user.id}`}
                      className="font-medium hover:underline truncate"
                    >
                      {user.displayName || user.username}
                    </Link>
                    {user.isVerified && (
                      <Verified className="h-4 w-4 text-blue-500 fill-current" />
                    )}
                  </div>
                  {user.bio && (
                    <p className="text-sm text-muted-foreground truncate">
                      {user.bio}
                    </p>
                  )}
                  {showStats && (
                    <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {formatCount(user.followerCount)}
                      </span>
                      <span className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        {formatCount(user.postCount)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <FollowButton
                userId={user.id}
                username={user.username}
                displayName={user.displayName}
                size="sm"
              />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (variant === 'grid') {
    return (
      <div className={cn('space-y-4', className)}>
        {title && (
          <h3 className="text-lg font-semibold">{title}</h3>
        )}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {displayUsers.map((user) => (
            <Card key={user.id} className="text-center">
              <CardContent className="p-4">
                <Link href={`/profile/${user.username || user.id}`}>
                  <Avatar className="h-16 w-16 mx-auto mb-3">
                    <AvatarImage src={user.avatarUrl} alt={user.displayName} />
                    <AvatarFallback>
                      {(user.displayName || user.username || 'U').charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Link>
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-1">
                    <Link 
                      href={`/profile/${user.username || user.id}`}
                      className="font-medium hover:underline truncate"
                    >
                      {user.displayName || user.username}
                    </Link>
                    {user.isVerified && (
                      <Verified className="h-4 w-4 text-blue-500 fill-current" />
                    )}
                  </div>
                  {showStats && (
                    <div className="flex justify-center gap-3 text-xs text-muted-foreground">
                      <span>{formatCount(user.followerCount)} followers</span>
                    </div>
                  )}
                  <FollowButton
                    userId={user.id}
                    username={user.username}
                    displayName={user.displayName}
                    size="sm"
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Default card variant
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {displayUsers.map((user) => (
          <div key={user.id} className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href={`/profile/${user.username || user.id}`}>
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user.avatarUrl} alt={user.displayName} />
                  <AvatarFallback>
                    {(user.displayName || user.username || 'U').charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </Link>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-1">
                  <Link 
                    href={`/profile/${user.username || user.id}`}
                    className="font-medium hover:underline truncate"
                  >
                    {user.displayName || user.username}
                  </Link>
                  {user.isVerified && (
                    <Verified className="h-4 w-4 text-blue-500 fill-current" />
                  )}
                </div>
                {showStats && (
                  <p className="text-sm text-muted-foreground">
                    {formatCount(user.followerCount)} followers
                  </p>
                )}
              </div>
            </div>
            <FollowButton
              userId={user.id}
              username={user.username}
              displayName={user.displayName}
              size="sm"
            />
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

function UserSuggestionsSkeleton({ 
  variant, 
  title 
}: { 
  variant: 'card' | 'list' | 'grid'
  title: string 
}) {
  const skeletonItems = Array.from({ length: 5 }, (_, i) => i)

  if (variant === 'list') {
    return (
      <div className="space-y-3">
        <Skeleton className="h-6 w-32" />
        <div className="space-y-2">
          {skeletonItems.map((i) => (
            <div key={i} className="flex items-center justify-between p-3 rounded-lg border">
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <Skeleton className="h-8 w-16" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (variant === 'grid') {
    return (
      <div className="space-y-4">
        <Skeleton className="h-6 w-32" />
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {skeletonItems.map((i) => (
            <Card key={i}>
              <CardContent className="p-4 text-center">
                <Skeleton className="h-16 w-16 rounded-full mx-auto mb-3" />
                <Skeleton className="h-4 w-20 mx-auto mb-2" />
                <Skeleton className="h-3 w-16 mx-auto mb-3" />
                <Skeleton className="h-8 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
      </CardHeader>
      <CardContent className="space-y-4">
        {skeletonItems.map((i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
