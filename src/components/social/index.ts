// Social Media Components Export
export { SocialInteractions } from './social-interactions'
export { FollowButton, CompactFollowButton, TextFollowButton } from './follow-button'
export { UserSuggestions } from './user-suggestions'
export { Notifications } from './notifications'
export { ContentCreator } from './content-creator'

// Social Hooks Export
export { useFollowSystem } from '../../hooks/social/use-follow-system'
export { useNotifications } from '../../hooks/social/use-notifications'

// Social Stores Export
export {
  useSocialStore,
  selectCurrentUser,
  selectFollowers,
  selectFollowing,
  selectNotifications,
  selectUnreadCount,
  selectSuggestedUsers,
  selectRecentlyViewed,
  selectIsFollowing,
  selectIsFollower,
  selectConnectionType,
} from '../../lib/stores/social-store'

export {
  useInteractionStore,
  selectUserReaction,
  selectHasMemory,
  selectHasShare,
  selectHasSave,
  selectIsReactionLoading,
  selectIsMemoryLoading,
  selectIsShareLoading,
  selectIsSaveLoading,
  selectViewCount,
  selectWatchTime,
} from '../../lib/stores/interaction-store'

export {
  useFeedStore,
  selectCurrentItem,
  selectIsItemPreloaded,
  selectFeedItems,
  selectCurrentIndex,
  selectLoading,
  selectHasMore,
  selectFilters,
  selectPreferences,
} from '../../lib/stores/feed-store'

export {
  useUserPreferencesStore,
  selectNotifications as selectNotificationPreferences,
  selectPrivacy,
  selectAccessibility,
  selectContent,
  selectTheme,
  selectLanguage,
  selectAutoPlay,
  selectMuteByDefault,
  selectPreferredQuality,
} from '../../lib/stores/user-preferences-store'
