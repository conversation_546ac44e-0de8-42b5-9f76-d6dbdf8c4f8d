"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Image, 
  Video, 
  Music, 
  FileText, 
  Sparkles, 
  Calendar, 
  Globe, 
  Users, 
  Lock,
  Upload,
  Loader2,
  X,
  Plus
} from 'lucide-react'
import { ContentType } from '@/types'
import { toast } from 'sonner'

interface ContentCreatorProps {
  className?: string
  onPublish?: (content: any) => void
  defaultMood?: string
  defaultContentType?: ContentType
}

const contentTypes = [
  { type: ContentType.TEXT, label: 'Text Post', icon: FileText },
  { type: ContentType.IMAGE, label: 'Image', icon: Image },
  { type: ContentType.VIDEO, label: 'Video', icon: Video },
  { type: ContentType.MUSIC, label: 'Audio', icon: Music },
]

const moods = [
  'Happy', 'Energetic', 'Chill', 'Creative', 'Nostalgic', 
  'Romantic', 'Melancholic', 'Excited', 'Peaceful', 'Mysterious'
]

const visibilityOptions = [
  { value: 'public', label: 'Public', icon: Globe, description: 'Anyone can see this post' },
  { value: 'followers', label: 'Followers', icon: Users, description: 'Only your followers can see this' },
  { value: 'private', label: 'Private', icon: Lock, description: 'Only you can see this post' },
]

export function ContentCreator({
  className,
  onPublish,
  defaultMood,
  defaultContentType = ContentType.TEXT,
}: ContentCreatorProps) {
  const [contentType, setContentType] = useState<ContentType>(defaultContentType)
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [selectedMoods, setSelectedMoods] = useState<string[]>(defaultMood ? [defaultMood] : [])
  const [visibility, setVisibility] = useState('public')
  const [scheduledDate, setScheduledDate] = useState('')
  const [files, setFiles] = useState<File[]>([])
  const [isPublishing, setIsPublishing] = useState(false)
  const [isDraft, setIsDraft] = useState(false)

  const handleMoodToggle = (mood: string) => {
    setSelectedMoods(prev => 
      prev.includes(mood) 
        ? prev.filter(m => m !== mood)
        : [...prev, mood]
    )
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    setFiles(prev => [...prev, ...selectedFiles])
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handlePublish = async (asDraft = false) => {
    if (!content.trim() && files.length === 0) {
      toast.error('Please add some content or media')
      return
    }

    setIsPublishing(true)
    setIsDraft(asDraft)

    try {
      const formData = new FormData()
      
      // Add text content
      formData.append('title', title)
      formData.append('content', content)
      formData.append('contentType', contentType)
      formData.append('moods', JSON.stringify(selectedMoods))
      formData.append('visibility', visibility)
      formData.append('isDraft', asDraft.toString())
      
      if (scheduledDate) {
        formData.append('scheduledDate', scheduledDate)
      }

      // Add files
      files.forEach((file, index) => {
        formData.append(`file_${index}`, file)
      })

      const response = await fetch('/api/posts/create', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Failed to create post')
      }

      const data = await response.json()
      
      toast.success(asDraft ? 'Draft saved!' : 'Post published!')
      
      // Reset form
      setTitle('')
      setContent('')
      setSelectedMoods([])
      setFiles([])
      setScheduledDate('')
      
      // Call callback if provided
      if (onPublish) {
        onPublish(data.post)
      }

    } catch (error) {
      console.error('Error publishing post:', error)
      toast.error('Failed to publish post')
    } finally {
      setIsPublishing(false)
      setIsDraft(false)
    }
  }

  const openStudio = () => {
    // Open AI Content Studio in a new tab or modal
    window.open('/studio', '_blank')
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Create Content
          </CardTitle>
          <Button variant="outline" size="sm" onClick={openStudio}>
            <Sparkles className="h-4 w-4 mr-2" />
            AI Studio
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Content Type Selection */}
        <div className="space-y-2">
          <Label>Content Type</Label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {contentTypes.map(({ type, label, icon: Icon }) => (
              <Button
                key={type}
                variant={contentType === type ? "default" : "outline"}
                size="sm"
                className="gap-2"
                onClick={() => setContentType(type)}
              >
                <Icon className="h-4 w-4" />
                {label}
              </Button>
            ))}
          </div>
        </div>

        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title">Title (Optional)</Label>
          <Input
            id="title"
            placeholder="Give your post a title..."
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>

        {/* Content */}
        <div className="space-y-2">
          <Label htmlFor="content">Content</Label>
          <Textarea
            id="content"
            placeholder="What's on your mind? Share your thoughts, feelings, or creative ideas..."
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={4}
            className="resize-none"
          />
        </div>

        {/* File Upload */}
        {contentType !== ContentType.TEXT && (
          <div className="space-y-2">
            <Label>Media</Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
              <div className="text-center">
                <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-2">
                  Drop files here or click to upload
                </p>
                <input
                  type="file"
                  multiple
                  accept={
                    contentType === ContentType.IMAGE ? 'image/*' :
                    contentType === ContentType.VIDEO ? 'video/*' :
                    contentType === ContentType.MUSIC ? 'audio/*' :
                    '*/*'
                  }
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <Button variant="outline" size="sm" asChild>
                  <label htmlFor="file-upload" className="cursor-pointer">
                    Choose Files
                  </label>
                </Button>
              </div>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-2">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <span className="text-sm truncate">{file.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Mood Selection */}
        <div className="space-y-2">
          <Label>Moods (Select up to 3)</Label>
          <div className="flex flex-wrap gap-2">
            {moods.map((mood) => (
              <Badge
                key={mood}
                variant={selectedMoods.includes(mood) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => handleMoodToggle(mood)}
              >
                {mood}
                {selectedMoods.includes(mood) && (
                  <X className="h-3 w-3 ml-1" />
                )}
              </Badge>
            ))}
          </div>
          {selectedMoods.length >= 3 && (
            <p className="text-xs text-muted-foreground">
              Maximum 3 moods selected
            </p>
          )}
        </div>

        {/* Advanced Options */}
        <Tabs defaultValue="visibility" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="visibility">Visibility</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
          </TabsList>
          
          <TabsContent value="visibility" className="space-y-3">
            {visibilityOptions.map((option) => {
              const Icon = option.icon
              return (
                <div
                  key={option.value}
                  className={cn(
                    'flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors',
                    visibility === option.value 
                      ? 'border-primary bg-primary/5' 
                      : 'hover:bg-muted/50'
                  )}
                  onClick={() => setVisibility(option.value)}
                >
                  <Icon className="h-5 w-5" />
                  <div className="flex-1">
                    <div className="font-medium">{option.label}</div>
                    <div className="text-sm text-muted-foreground">
                      {option.description}
                    </div>
                  </div>
                </div>
              )
            })}
          </TabsContent>
          
          <TabsContent value="schedule" className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="schedule">Schedule for later (Optional)</Label>
              <Input
                id="schedule"
                type="datetime-local"
                value={scheduledDate}
                onChange={(e) => setScheduledDate(e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
              />
            </div>
          </TabsContent>
        </Tabs>

        {/* Actions */}
        <div className="flex gap-2 pt-4">
          <Button
            variant="outline"
            onClick={() => handlePublish(true)}
            disabled={isPublishing}
            className="flex-1"
          >
            {isPublishing && isDraft ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FileText className="h-4 w-4 mr-2" />
            )}
            Save Draft
          </Button>
          <Button
            onClick={() => handlePublish(false)}
            disabled={isPublishing}
            className="flex-1"
          >
            {isPublishing && !isDraft ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : scheduledDate ? (
              <Calendar className="h-4 w-4 mr-2" />
            ) : (
              <Globe className="h-4 w-4 mr-2" />
            )}
            {scheduledDate ? 'Schedule' : 'Publish'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
