"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  Bookmark,
  BookmarkCheck,
  Loader2,
  Flame,
  Zap,
  Sparkles
} from 'lucide-react'
import { ReactionType } from '@/types'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
import { toast } from 'sonner'

interface SocialInteractionsProps {
  postId: string
  likeCount?: number
  commentCount?: number
  shareCount?: number
  className?: string
  variant?: 'default' | 'compact' | 'vertical'
  showCounts?: boolean
  showLabels?: boolean
}

const reactionIcons = {
  [ReactionType.LIKE]: Heart,
  [ReactionType.LOVE]: Heart,
  [ReactionType.FIRE]: Flame,
  [ReactionType.MIND_BLOWN]: Zap,
  [ReactionType.VIBE]: Sparkles,
  [ReactionType.MOOD_MATCH]: Sparkles,
}

const reactionColors = {
  [ReactionType.LIKE]: 'text-red-500',
  [ReactionType.LOVE]: 'text-pink-500',
  [ReactionType.FIRE]: 'text-orange-500',
  [ReactionType.MIND_BLOWN]: 'text-yellow-500',
  [ReactionType.VIBE]: 'text-purple-500',
  [ReactionType.MOOD_MATCH]: 'text-blue-500',
}

export function SocialInteractions({
  postId,
  likeCount = 0,
  commentCount = 0,
  shareCount = 0,
  className,
  variant = 'default',
  showCounts = true,
  showLabels = false,
}: SocialInteractionsProps) {
  const [showReactionPicker, setShowReactionPicker] = useState(false)
  const [showCommentInput, setShowCommentInput] = useState(false)
  const [commentText, setCommentText] = useState('')

  const {
    react,
    unreact,
    toggleMemory,
    toggleSave,
    share,
    comment,
    getUserReaction,
    hasMemory,
    hasSave,
    isReactionLoading,
    isMemoryLoading,
    isShareLoading,
    isSaveLoading,
  } = useContentInteractions()

  const currentReaction = getUserReaction(postId)
  const isMemorySaved = hasMemory(postId)
  const isPostSaved = hasSave(postId)

  const handleReaction = async (reactionType: ReactionType) => {
    try {
      if (currentReaction === reactionType) {
        await unreact(postId)
      } else {
        await react(postId, reactionType)
      }
      setShowReactionPicker(false)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleShare = async () => {
    try {
      await share(postId)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleSave = async () => {
    try {
      await toggleSave(postId)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleComment = async () => {
    if (!commentText.trim()) return
    
    try {
      await comment(postId, commentText.trim())
      setCommentText('')
      setShowCommentInput(false)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const formatCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  const isVertical = variant === 'vertical'
  const isCompact = variant === 'compact'

  return (
    <div className={cn(
      'flex gap-2',
      isVertical ? 'flex-col items-center' : 'items-center',
      className
    )}>
      {/* Like/Reaction Button */}
      <div className="relative">
        <Button
          variant="ghost"
          size={isCompact ? "sm" : "default"}
          className={cn(
            'gap-2 transition-all duration-200',
            currentReaction && reactionColors[currentReaction],
            isVertical && 'flex-col h-auto p-2'
          )}
          onClick={() => setShowReactionPicker(!showReactionPicker)}
          disabled={isReactionLoading(postId)}
        >
          {isReactionLoading(postId) ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : currentReaction ? (
            React.createElement(reactionIcons[currentReaction], {
              className: cn('h-4 w-4', currentReaction && 'fill-current')
            })
          ) : (
            <Heart className="h-4 w-4" />
          )}
          {showCounts && (
            <span className={cn(
              'text-sm',
              isVertical && 'text-xs'
            )}>
              {formatCount(likeCount)}
            </span>
          )}
          {showLabels && !isVertical && (
            <span className="text-sm">Like</span>
          )}
        </Button>

        {/* Reaction Picker */}
        {showReactionPicker && (
          <div className="absolute bottom-full left-0 mb-2 bg-background border rounded-lg shadow-lg p-2 flex gap-1 z-50">
            {Object.values(ReactionType).map((reactionType) => {
              const Icon = reactionIcons[reactionType]
              return (
                <Button
                  key={reactionType}
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'p-2 h-auto',
                    reactionColors[reactionType]
                  )}
                  onClick={() => handleReaction(reactionType)}
                >
                  <Icon className="h-4 w-4" />
                </Button>
              )
            })}
          </div>
        )}
      </div>

      {/* Comment Button */}
      <Button
        variant="ghost"
        size={isCompact ? "sm" : "default"}
        className={cn(
          'gap-2',
          isVertical && 'flex-col h-auto p-2'
        )}
        onClick={() => setShowCommentInput(!showCommentInput)}
      >
        <MessageCircle className="h-4 w-4" />
        {showCounts && (
          <span className={cn(
            'text-sm',
            isVertical && 'text-xs'
          )}>
            {formatCount(commentCount)}
          </span>
        )}
        {showLabels && !isVertical && (
          <span className="text-sm">Comment</span>
        )}
      </Button>

      {/* Share Button */}
      <Button
        variant="ghost"
        size={isCompact ? "sm" : "default"}
        className={cn(
          'gap-2',
          isVertical && 'flex-col h-auto p-2'
        )}
        onClick={handleShare}
        disabled={isShareLoading(postId)}
      >
        {isShareLoading(postId) ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Share2 className="h-4 w-4" />
        )}
        {showCounts && (
          <span className={cn(
            'text-sm',
            isVertical && 'text-xs'
          )}>
            {formatCount(shareCount)}
          </span>
        )}
        {showLabels && !isVertical && (
          <span className="text-sm">Share</span>
        )}
      </Button>

      {/* Save Button */}
      <Button
        variant="ghost"
        size={isCompact ? "sm" : "default"}
        className={cn(
          'gap-2',
          isVertical && 'flex-col h-auto p-2',
          isPostSaved && 'text-yellow-500'
        )}
        onClick={handleSave}
        disabled={isSaveLoading(postId)}
      >
        {isSaveLoading(postId) ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : isPostSaved ? (
          <BookmarkCheck className="h-4 w-4 fill-current" />
        ) : (
          <Bookmark className="h-4 w-4" />
        )}
        {showLabels && !isVertical && (
          <span className="text-sm">
            {isPostSaved ? 'Saved' : 'Save'}
          </span>
        )}
      </Button>

      {/* Comment Input */}
      {showCommentInput && (
        <div className="absolute top-full left-0 mt-2 w-full max-w-md bg-background border rounded-lg shadow-lg p-3 z-50">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Add a comment..."
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              className="flex-1 px-3 py-2 border rounded-md text-sm"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleComment()
                }
              }}
            />
            <Button
              size="sm"
              onClick={handleComment}
              disabled={!commentText.trim()}
            >
              Post
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
