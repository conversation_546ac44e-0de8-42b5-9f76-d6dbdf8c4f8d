"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { UserPlus, UserMinus, Users, Loader2 } from 'lucide-react'
import { useFollowSystem } from '@/hooks/social/use-follow-system'
import { useSocialStore, selectConnectionType } from '@/lib/stores/social-store'

interface FollowButtonProps {
  userId: string
  username?: string
  displayName?: string
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  showIcon?: boolean
  showText?: boolean
  disabled?: boolean
}

export function FollowButton({
  userId,
  username,
  displayName,
  className,
  variant = 'default',
  size = 'default',
  showIcon = true,
  showText = true,
  disabled = false,
}: FollowButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  
  const { followUser, unfollowUser } = useFollowSystem()
  const connectionType = useSocialStore(selectConnectionType(userId))

  const handleFollow = async () => {
    if (isLoading || disabled) return

    setIsLoading(true)
    try {
      if (connectionType === 'following' || connectionType === 'mutual') {
        await unfollowUser(userId)
      } else {
        await followUser(userId)
      }
    } catch (error) {
      // Error handling is done in the hook
    } finally {
      setIsLoading(false)
    }
  }

  const getButtonContent = () => {
    const isFollowing = connectionType === 'following' || connectionType === 'mutual'
    const isMutual = connectionType === 'mutual'
    
    if (isLoading) {
      return (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          {showText && <span>Loading...</span>}
        </>
      )
    }

    if (isFollowing) {
      return (
        <>
          {showIcon && (isMutual ? <Users className="h-4 w-4" /> : <UserMinus className="h-4 w-4" />)}
          {showText && (
            <span>
              {isMutual ? 'Mutual' : 'Following'}
            </span>
          )}
        </>
      )
    }

    return (
      <>
        {showIcon && <UserPlus className="h-4 w-4" />}
        {showText && <span>Follow</span>}
      </>
    )
  }

  const getButtonVariant = () => {
    const isFollowing = connectionType === 'following' || connectionType === 'mutual'
    
    if (isFollowing) {
      return variant === 'default' ? 'outline' : variant
    }
    
    return variant
  }

  const getButtonClassName = () => {
    const isFollowing = connectionType === 'following' || connectionType === 'mutual'
    const isMutual = connectionType === 'mutual'
    
    return cn(
      'gap-2 transition-all duration-200',
      isFollowing && 'hover:bg-destructive hover:text-destructive-foreground',
      isMutual && 'bg-blue-500 hover:bg-blue-600 text-white',
      className
    )
  }

  // Don't show follow button for self
  if (!userId || userId === useSocialStore.getState().currentUser?.id) {
    return null
  }

  return (
    <Button
      variant={getButtonVariant()}
      size={size}
      className={getButtonClassName()}
      onClick={handleFollow}
      disabled={disabled || isLoading}
      aria-label={
        connectionType === 'following' || connectionType === 'mutual'
          ? `Unfollow ${displayName || username}`
          : `Follow ${displayName || username}`
      }
    >
      {getButtonContent()}
    </Button>
  )
}

// Compact version for use in lists
export function CompactFollowButton({
  userId,
  className,
  ...props
}: Omit<FollowButtonProps, 'showText' | 'size'>) {
  return (
    <FollowButton
      userId={userId}
      className={cn('h-8 w-8 p-0', className)}
      size="sm"
      showText={false}
      showIcon={true}
      {...props}
    />
  )
}

// Text-only version for use in dropdowns
export function TextFollowButton({
  userId,
  className,
  ...props
}: Omit<FollowButtonProps, 'showIcon' | 'variant'>) {
  return (
    <FollowButton
      userId={userId}
      className={cn('justify-start', className)}
      variant="ghost"
      showIcon={false}
      showText={true}
      {...props}
    />
  )
}
