"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Bell, 
  BellOff, 
  Heart, 
  MessageCircle, 
  UserPlus, 
  Share2, 
  Bookmark,
  Sparkles,
  X,
  Check,
  CheckCheck
} from 'lucide-react'
import { useNotifications } from '@/hooks/social/use-notifications'
import { formatDistanceToNow } from 'date-fns'
import Link from 'next/link'

interface NotificationsProps {
  className?: string
  variant?: 'dropdown' | 'page' | 'sidebar'
  showHeader?: boolean
  maxHeight?: string
}

const notificationIcons = {
  like: Heart,
  comment: MessageCircle,
  follow: UserPlus,
  share: Share2,
  memory: Bookmark,
  mood_match: Sparkles,
  mention: MessageCircle,
}

const notificationColors = {
  like: 'text-red-500',
  comment: 'text-blue-500',
  follow: 'text-green-500',
  share: 'text-purple-500',
  memory: 'text-yellow-500',
  mood_match: 'text-pink-500',
  mention: 'text-blue-500',
}

export function Notifications({
  className,
  variant = 'dropdown',
  showHeader = true,
  maxHeight = '400px',
}: NotificationsProps) {
  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
  } = useNotifications()

  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set())

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead()
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleDelete = async (notificationId: string) => {
    setDeletingIds(prev => new Set([...prev, notificationId]))
    try {
      await deleteNotification(notificationId)
    } catch (error) {
      // Error handling is done in the hook
    } finally {
      setDeletingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(notificationId)
        return newSet
      })
    }
  }

  const getNotificationLink = (notification: any) => {
    if (notification.postId) {
      return `/post/${notification.postId}`
    }
    if (notification.fromUser?.username) {
      return `/profile/${notification.fromUser.username}`
    }
    return '#'
  }

  if (isLoading && notifications.length === 0) {
    return <NotificationsSkeleton variant={variant} />
  }

  const content = (
    <div className="space-y-1">
      {notifications.length === 0 ? (
        <div className="text-center py-8">
          <BellOff className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No notifications</h3>
          <p className="text-muted-foreground text-sm">
            You're all caught up! Check back later for updates.
          </p>
        </div>
      ) : (
        notifications.map((notification) => {
          const Icon = notificationIcons[notification.type as keyof typeof notificationIcons] || Bell
          const iconColor = notificationColors[notification.type as keyof typeof notificationColors] || 'text-gray-500'
          const isDeleting = deletingIds.has(notification.id)

          return (
            <div
              key={notification.id}
              className={cn(
                'flex items-start gap-3 p-3 rounded-lg transition-all duration-200',
                !notification.isRead && 'bg-blue-50 dark:bg-blue-950/20',
                'hover:bg-accent/50',
                isDeleting && 'opacity-50'
              )}
            >
              {/* User Avatar */}
              <Link href={`/profile/${notification.fromUser?.username || notification.fromUser?.id}`}>
                <Avatar className="h-10 w-10">
                  <AvatarImage 
                    src={notification.fromUser?.avatarUrl} 
                    alt={notification.fromUser?.displayName} 
                  />
                  <AvatarFallback>
                    {(notification.fromUser?.displayName || notification.fromUser?.username || 'U').charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </Link>

              {/* Notification Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <Link 
                      href={getNotificationLink(notification)}
                      className="block hover:underline"
                      onClick={() => !notification.isRead && handleMarkAsRead(notification.id)}
                    >
                      <p className="text-sm">
                        <span className="font-medium">
                          {notification.fromUser?.displayName || notification.fromUser?.username}
                        </span>{' '}
                        {notification.message}
                      </p>
                      
                      {/* Post preview if available */}
                      {notification.post && (
                        <div className="mt-2 p-2 bg-muted rounded text-xs">
                          {notification.post.title && (
                            <p className="font-medium truncate">{notification.post.title}</p>
                          )}
                          <p className="text-muted-foreground truncate">
                            {notification.post.content}
                          </p>
                        </div>
                      )}
                    </Link>

                    <div className="flex items-center gap-2 mt-1">
                      <Icon className={cn('h-3 w-3', iconColor)} />
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                      </span>
                      {!notification.isRead && (
                        <Badge variant="secondary" className="h-2 w-2 p-0 bg-blue-500" />
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1">
                    {!notification.isRead && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => handleMarkAsRead(notification.id)}
                        title="Mark as read"
                      >
                        <Check className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                      onClick={() => handleDelete(notification.id)}
                      disabled={isDeleting}
                      title="Delete notification"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )
        })
      )}
    </div>
  )

  if (variant === 'page') {
    return (
      <div className={cn('space-y-6', className)}>
        {showHeader && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">Notifications</h1>
              {unreadCount > 0 && (
                <Badge variant="secondary">{unreadCount}</Badge>
              )}
            </div>
            {unreadCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="gap-2"
              >
                <CheckCheck className="h-4 w-4" />
                Mark all as read
              </Button>
            )}
          </div>
        )}
        <div className="space-y-2">
          {content}
        </div>
      </div>
    )
  }

  if (variant === 'sidebar') {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
                {unreadCount > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {unreadCount}
                  </Badge>
                )}
              </CardTitle>
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  title="Mark all as read"
                >
                  <CheckCheck className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardHeader>
        )}
        <CardContent className="p-0">
          <ScrollArea style={{ maxHeight }}>
            <div className="p-3">
              {content}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    )
  }

  // Default dropdown variant
  return (
    <div className={cn('w-80', className)} style={{ maxHeight }}>
      {showHeader && (
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="font-semibold flex items-center gap-2">
            Notifications
            {unreadCount > 0 && (
              <Badge variant="secondary">{unreadCount}</Badge>
            )}
          </h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              title="Mark all as read"
            >
              <CheckCheck className="h-4 w-4" />
            </Button>
          )}
        </div>
      )}
      <ScrollArea style={{ maxHeight: showHeader ? `calc(${maxHeight} - 60px)` : maxHeight }}>
        <div className="p-1">
          {content}
        </div>
      </ScrollArea>
    </div>
  )
}

function NotificationsSkeleton({ variant }: { variant: string }) {
  const skeletonItems = Array.from({ length: 5 }, (_, i) => i)

  const skeletonContent = (
    <div className="space-y-3">
      {skeletonItems.map((i) => (
        <div key={i} className="flex items-start gap-3 p-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  )

  if (variant === 'page') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-24" />
        </div>
        {skeletonContent}
      </div>
    )
  }

  if (variant === 'sidebar') {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          {skeletonContent}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-80">
      <div className="p-3 border-b">
        <Skeleton className="h-6 w-24" />
      </div>
      <div className="p-1">
        {skeletonContent}
      </div>
    </div>
  )
}
