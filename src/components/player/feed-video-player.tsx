"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { VideoPlayer } from './video-player'
import { useVideoPlayerWithIntersection } from '@/hooks/player/use-video-player'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { MediaSource } from '@/lib/player/types'

interface FeedVideoPlayerProps {
  contentId: string
  source: MediaSource
  metadata?: any
  className?: string
  onLike?: () => void
  onShare?: () => void
  onComment?: () => void
  onSave?: () => void
  autoPlayThreshold?: number
  priority?: 'high' | 'normal' | 'low'
}

export function FeedVideoPlayer({
  contentId,
  source,
  metadata,
  className,
  onLike,
  onShare,
  onComment,
  onSave,
  autoPlayThreshold = 0.7,
  priority = 'normal'
}: FeedVideoPlayerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLiked, setIsLiked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)
  const [viewStartTime, setViewStartTime] = useState<number | null>(null)
  
  const { addToQueue } = usePlayerStore()

  // Use intersection observer for auto-play
  const {
    player,
    state,
    isInView,
    play,
    pause
  } = useVideoPlayerWithIntersection(containerRef, {
    element: videoRef.current || undefined,
    source,
    config: {
      autoPlay: false, // Controlled by intersection
      muted: true,
      loop: true,
      preload: priority === 'high' ? 'auto' : 'metadata'
    },
    threshold: autoPlayThreshold
  })

  // Track view time for analytics
  useEffect(() => {
    if (isInView && state.state === 'playing' && !viewStartTime) {
      setViewStartTime(Date.now())
    } else if (!isInView && viewStartTime) {
      const viewDuration = Date.now() - viewStartTime
      // Track view analytics
      console.log(`Video ${contentId} viewed for ${viewDuration}ms`)
      setViewStartTime(null)
    }
  }, [isInView, state.state, viewStartTime, contentId])

  // Handle social actions
  const handleLike = useCallback(() => {
    setIsLiked(!isLiked)
    onLike?.()
    
    // Track engagement
    console.log(`Video ${contentId} ${!isLiked ? 'liked' : 'unliked'}`)
  }, [isLiked, onLike, contentId])

  const handleSave = useCallback(() => {
    setIsSaved(!isSaved)
    onSave?.()
    
    // Add to queue if saved
    if (!isSaved && source) {
      addToQueue({
        id: contentId,
        source,
        metadata,
        duration: state.duration
      })
    }
    
    console.log(`Video ${contentId} ${!isSaved ? 'saved' : 'unsaved'}`)
  }, [isSaved, onSave, contentId, source, metadata, state.duration, addToQueue])

  const handleShare = useCallback(() => {
    onShare?.()
    
    // Native share if available
    if (navigator.share && metadata?.title) {
      navigator.share({
        title: metadata.title,
        text: metadata.description || `Check out this video by ${metadata.artist || 'HVPPY'}`,
        url: window.location.href
      }).catch(console.error)
    } else {
      // Fallback to clipboard
      navigator.clipboard?.writeText(window.location.href)
    }
    
    console.log(`Video ${contentId} shared`)
  }, [onShare, contentId, metadata])

  const handleComment = useCallback(() => {
    onComment?.()
    console.log(`Video ${contentId} comment action`)
  }, [onComment, contentId])

  // Preload next video in feed (optimization)
  useEffect(() => {
    if (isInView && priority === 'high') {
      // Preload logic could be implemented here
      console.log(`Preloading next video after ${contentId}`)
    }
  }, [isInView, priority, contentId])

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative w-full h-full bg-black overflow-hidden',
        className
      )}
      data-content-id={contentId}
    >
      <VideoPlayer
        ref={videoRef}
        source={source}
        className="w-full h-full"
        autoPlay={false} // Controlled by intersection observer
        muted={true}
        loop={true}
        showControls={true}
        showOverlay={true}
        aspectRatio="9:16"
        title={metadata?.title}
        creator={metadata?.artist || 'HVPPY'}
        description={metadata?.description}
        tags={metadata?.tags}
        moods={metadata?.moods}
        poster={metadata?.thumbnail}
      />

      {/* Feed-specific overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Right side actions */}
        <div className="absolute right-4 bottom-20 flex flex-col space-y-6 pointer-events-auto">
          {/* Like button */}
          <button
            onClick={handleLike}
            className={cn(
              "flex flex-col items-center space-y-1 transition-colors",
              isLiked ? "text-red-500" : "text-white"
            )}
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center">
              <svg
                className={cn("w-6 h-6", isLiked && "fill-current")}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </div>
            <span className="text-xs font-medium">
              {metadata?.likes || 0}
            </span>
          </button>

          {/* Comment button */}
          <button
            onClick={handleComment}
            className="flex flex-col items-center space-y-1 text-white"
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center">
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <span className="text-xs font-medium">
              {metadata?.comments || 0}
            </span>
          </button>

          {/* Save button */}
          <button
            onClick={handleSave}
            className={cn(
              "flex flex-col items-center space-y-1 transition-colors",
              isSaved ? "text-yellow-500" : "text-white"
            )}
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center">
              <svg
                className={cn("w-6 h-6", isSaved && "fill-current")}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                />
              </svg>
            </div>
            <span className="text-xs font-medium">Save</span>
          </button>

          {/* Share button */}
          <button
            onClick={handleShare}
            className="flex flex-col items-center space-y-1 text-white"
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center">
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                />
              </svg>
            </div>
            <span className="text-xs font-medium">Share</span>
          </button>
        </div>

        {/* Bottom info overlay */}
        <div className="absolute bottom-0 left-0 right-16 p-4 bg-gradient-to-t from-black/60 to-transparent pointer-events-auto">
          <div className="space-y-2">
            <h3 className="text-white font-semibold text-lg">
              {metadata?.title}
            </h3>
            <p className="text-white/80 text-sm">
              @{metadata?.artist || 'hvppy'}
            </p>
            {metadata?.description && (
              <p className="text-white/70 text-sm line-clamp-2">
                {metadata.description}
              </p>
            )}
            {metadata?.moods && metadata.moods.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {metadata.moods.slice(0, 3).map((mood: string) => (
                  <span
                    key={mood}
                    className="px-2 py-1 bg-purple-500/20 text-purple-200 text-xs rounded-full"
                  >
                    {mood}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* View indicator */}
        {isInView && (
          <div className="absolute top-4 left-4 px-2 py-1 bg-green-500/20 text-green-200 text-xs rounded-full">
            In View
          </div>
        )}
      </div>
    </div>
  )
}
