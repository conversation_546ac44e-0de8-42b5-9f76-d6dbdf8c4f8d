"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize2,
  Minimize2,
  Loader2,
  AlertCircle,
  Music
} from 'lucide-react'

interface HVPPYContentPlayerProps {
  contentUrl: string
  contentType: 'video' | 'audio'
  className?: string
  autoPlay?: boolean
  showControls?: boolean
  muted?: boolean
  loop?: boolean
  title?: string
  artist?: string
  onPlayerReady?: (element: HTMLVideoElement | HTMLAudioElement) => void
  onError?: (error: string) => void
}

export function HVPPYContentPlayer({
  contentUrl,
  contentType,
  className,
  autoPlay = false,
  showControls = true,
  muted = true,
  loop = true,
  title,
  artist,
  onPlayerReady,
  onError
}: HVPPYContentPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(muted)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showControlsOverlay, setShowControlsOverlay] = useState(true)
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null)

  const mediaElement = contentType === 'video' ? videoRef.current : audioRef.current
  }, [contentId, onError])

  // Video player hook
  const videoPlayer = useHVPPYVideoPlayer(contentType === 'video' ? contentId : undefined)
  
  // Audio player hook  
  const audioPlayer = useHVPPYAudioPlayer(contentType === 'audio' ? contentId : undefined)

  // Handle player ready
  useEffect(() => {
    const player = contentType === 'video' ? videoPlayer.player : audioPlayer.player
    if (player) {
      onPlayerReady?.(player)
    }
  }, [contentType, videoPlayer.player, audioPlayer.player, onPlayerReady])

  // Handle errors
  useEffect(() => {
    const error = contentType === 'video' ? videoPlayer.error : audioPlayer.error
    if (error) {
      onError?.(error)
    }
  }, [contentType, videoPlayer.error, audioPlayer.error, onError])

  // Loading state
  if (!contentType || (contentType === 'video' ? videoPlayer.loading : audioPlayer.loading)) {
    return (
      <div className={cn(
        'flex items-center justify-center p-8 bg-gray-900/50 rounded-lg',
        className
      )}>
        <div className="text-center text-white">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm">Loading content...</p>
        </div>
      </div>
    )
  }

  // Error state
  const error = contentType === 'video' ? videoPlayer.error : audioPlayer.error
  if (error) {
    return (
      <div className={cn(
        'flex items-center justify-center p-8 bg-gray-900/50 rounded-lg',
        className
      )}>
        <div className="text-center text-white">
          <AlertCircle className="w-8 h-8 text-red-400 mx-auto mb-2" />
          <p className="text-sm font-semibold mb-2">Failed to Load Content</p>
          <p className="text-xs opacity-70 mb-4">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white border-white/20 hover:bg-white/10"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // Get metadata
  const metadata = contentType === 'video' ? videoPlayer.metadata : audioPlayer.metadata

  // Render appropriate player
  if (contentType === 'video') {
    return (
      <VideoPlayer
        ref={(el) => setPlayerElement(el)}
        source={videoPlayer.source}
        className={className}
        autoPlay={autoPlay}
        showControls={showControls}
        showOverlay={true}
        title={metadata?.title}
        creator={metadata?.artist}
        description={metadata?.description}
        tags={metadata?.tags}
        moods={metadata?.moods}
        poster={metadata?.thumbnail}
        aspectRatio={variant === 'compact' ? '16:9' : 'auto'}
        onPlayerReady={onPlayerReady}
        onError={onError}
      />
    )
  } else {
    return (
      <AudioPlayer
        ref={(el) => setPlayerElement(el)}
        source={audioPlayer.source}
        className={className}
        autoPlay={autoPlay}
        showControls={showControls}
        showVisualizer={variant !== 'minimal'}
        variant={variant}
        title={metadata?.title}
        artist={metadata?.artist}
        album={metadata?.album}
        artwork={metadata?.artwork}
        onPlayerReady={onPlayerReady}
        onError={onError}
      />
    )
  }
}

// Specialized variants for different use cases
export function HVPPYFeedPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="default"
      autoPlay={true}
      showControls={true}
      {...props}
    />
  )
}

export function HVPPYCompactPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="compact"
      autoPlay={false}
      showControls={true}
      {...props}
    />
  )
}

export function HVPPYMinimalPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="minimal"
      autoPlay={false}
      showControls={false}
      {...props}
    />
  )
}

export function HVPPYCardPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="card"
      autoPlay={false}
      showControls={true}
      {...props}
    />
  )
}
