"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Activity, 
  Zap, 
  Clock, 
  MemoryStick, 
  Play, 
  ChevronDown,
  ChevronUp,
  Gauge
} from 'lucide-react'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  loadTime: number
  scrollPerformance: number
}

interface MediaElement {
  id: string
  type: 'video' | 'audio'
  isPlaying: boolean
}

interface FeedPerformanceMonitorProps {
  metrics: PerformanceMetrics
  isScrolling: boolean
  currentlyPlaying: MediaElement[]
  className?: string
}

export function FeedPerformanceMonitor({
  metrics,
  isScrolling,
  currentlyPlaying,
  className
}: FeedPerformanceMonitorProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-400'
    if (value >= thresholds.warning) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getPerformanceStatus = () => {
    const fpsGood = metrics.fps >= 55
    const memoryGood = metrics.memoryUsage < 100
    const scrollGood = metrics.scrollPerformance >= 80
    
    if (fpsGood && memoryGood && scrollGood) return 'excellent'
    if (metrics.fps >= 45 && metrics.scrollPerformance >= 60) return 'good'
    if (metrics.fps >= 30) return 'fair'
    return 'poor'
  }

  const status = getPerformanceStatus()
  const statusColors = {
    excellent: 'bg-green-500/20 text-green-400 border-green-500/30',
    good: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    fair: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    poor: 'bg-red-500/20 text-red-400 border-red-500/30'
  }

  const formatMemory = (bytes: number) => {
    if (bytes < 1024) return `${bytes}B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`
  }

  return (
    <div className={cn(
      "bg-black/40 backdrop-blur-md border border-white/20 rounded-lg overflow-hidden",
      className
    )}>
      {/* Compact Header */}
      <Button
        variant="ghost"
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 h-auto justify-between text-white hover:bg-white/10"
      >
        <div className="flex items-center space-x-2">
          <Activity className="w-4 h-4" />
          <Badge className={cn("text-xs", statusColors[status])}>
            {status.toUpperCase()}
          </Badge>
          <span className="text-xs">{Math.round(metrics.fps)} FPS</span>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </Button>

      {/* Expanded Details */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t border-white/10"
          >
            <div className="p-3 space-y-3">
              {/* Performance Metrics */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                {/* FPS */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Zap className="w-3 h-3 text-white/60" />
                    <span className="text-white/80">FPS</span>
                  </div>
                  <span className={getPerformanceColor(metrics.fps, { good: 55, warning: 45 })}>
                    {Math.round(metrics.fps)}
                  </span>
                </div>

                {/* Memory */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <MemoryStick className="w-3 h-3 text-white/60" />
                    <span className="text-white/80">Memory</span>
                  </div>
                  <span className={getPerformanceColor(100 - metrics.memoryUsage, { good: 70, warning: 50 })}>
                    {formatMemory(metrics.memoryUsage)}
                  </span>
                </div>

                {/* Load Time */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 text-white/60" />
                    <span className="text-white/80">Load</span>
                  </div>
                  <span className={getPerformanceColor(5000 - metrics.loadTime, { good: 3000, warning: 1000 })}>
                    {Math.round(metrics.loadTime)}ms
                  </span>
                </div>

                {/* Scroll Performance */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Gauge className="w-3 h-3 text-white/60" />
                    <span className="text-white/80">Scroll</span>
                  </div>
                  <span className={getPerformanceColor(metrics.scrollPerformance, { good: 80, warning: 60 })}>
                    {Math.round(metrics.scrollPerformance)}%
                  </span>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    isScrolling ? "bg-blue-400" : "bg-gray-500"
                  )} />
                  <span className="text-white/60">
                    {isScrolling ? "Scrolling" : "Idle"}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <Play className="w-3 h-3 text-white/60" />
                  <span className="text-white/60">
                    {currentlyPlaying.length} playing
                  </span>
                </div>
              </div>

              {/* Currently Playing Media */}
              {currentlyPlaying.length > 0 && (
                <div className="space-y-1">
                  <div className="text-xs text-white/60">Active Media:</div>
                  {currentlyPlaying.slice(0, 3).map((media) => (
                    <div key={media.id} className="flex items-center justify-between text-xs">
                      <span className="text-white/80 truncate">
                        {media.type} #{media.id.slice(-4)}
                      </span>
                      <div className="flex items-center space-x-1">
                        <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse" />
                        <span className="text-green-400">Playing</span>
                      </div>
                    </div>
                  ))}
                  {currentlyPlaying.length > 3 && (
                    <div className="text-xs text-white/50">
                      +{currentlyPlaying.length - 3} more
                    </div>
                  )}
                </div>
              )}

              {/* Performance Tips */}
              {status === 'poor' && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-500/10 border border-red-500/20 rounded p-2"
                >
                  <div className="text-xs text-red-400 space-y-1">
                    <div className="font-medium">Performance Tips:</div>
                    {metrics.fps < 30 && (
                      <div>• Close other apps to improve FPS</div>
                    )}
                    {metrics.memoryUsage > 150 && (
                      <div>• High memory usage detected</div>
                    )}
                    {metrics.scrollPerformance < 60 && (
                      <div>• Reduce scroll speed for smoother experience</div>
                    )}
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
