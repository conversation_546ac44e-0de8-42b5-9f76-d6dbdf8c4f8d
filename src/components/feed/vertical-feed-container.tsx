"use client"

import React, { useEffect, useCallback, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { VerticalFeedContainerProps, InteractionData } from '@/types/feed'
import { useFeedData } from '@/hooks/feed/use-feed-data'
import { useInfiniteScroll } from '@/hooks/feed/use-infinite-scroll'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
import { useContentPreloader } from '@/hooks/feed/use-content-preloader'
import { useMediaAutoplay } from '@/hooks/feed/use-media-autoplay'
import { useSwipeGestures } from '@/hooks/feed/use-swipe-gestures'
import { usePerformanceMonitor } from '@/hooks/feed/use-performance-monitor'
import { EnhancedContentCard } from './enhanced-content-card'
// import { FeedNavigation } from './feed-navigation'
// import { MoodSelector } from './mood-selector'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { usePlayerS<PERSON> } from '@/lib/stores/enhanced-player-store'
import { Loader2, RefreshCw, AlertCircle, Heart } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function VerticalFeedContainer({
  feedType,
  filters,
  onItemChange,
  onInteraction,
  className,
  autoPlay = true,
}: VerticalFeedContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)


  const {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters,
  } = useFeedData(feedType, filters)

  const {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
  } = useMoodFilter()

  const {
    react,
    unreact,
    toggleMemory,
    share,
  } = useContentInteractions()

  // Initialize performance hooks
  const infiniteScrollHook = useInfiniteScroll({
    threshold: 0.6,
    onItemChange: (index) => {
      setCurrentIndex(index)
      onItemChange?.(items[index], index)
    },
    preloadDistance: 3,
    offloadDistance: 10,
    enableVirtualization: true,
  })

  // Content preloader
  const {
    preloadedContent,
    getPreloadedContent,
    isPreloading,
    preloadedCount,
  } = useContentPreloader(items, currentIndex, {
    preloadDistance: 3,
    offloadDistance: 10,
    enableImagePreload: true,
    enableVideoPreload: true,
    enableAudioPreload: false, // Audio preload can be expensive
  })

  // Media autoplay management
  const {
    registerMediaElement,
    unregisterMediaElement,
    observeElement,
  } = useMediaAutoplay(items, currentIndex, {
    threshold: 0.6,
    autoplayDelay: 200,
    pauseDelay: 300,
    enableVideoAutoplay: autoPlay,
    enableAudioAutoplay: false,
    muteByDefault: true,
  })

  // Performance monitoring
  const {
    metrics: performanceMetrics,
    trackContentLoad,
    markStart,
    markEnd,
  } = usePerformanceMonitor({
    enabled: process.env.NODE_ENV === 'development',
    onPerformanceIssue: (issue, severity) => {
      console.warn(`Performance issue (${severity}):`, issue)
    },
  })

  // Swipe gesture support
  const { elementRef: swipeRef, isGesturing } = useSwipeGestures({
    onSwipeUp: () => {
      if (currentIndex < items.length - 1) {
        infiniteScrollHook.scrollToIndex(currentIndex + 1)
      }
    },
    onSwipeDown: () => {
      if (currentIndex > 0) {
        infiniteScrollHook.scrollToIndex(currentIndex - 1)
      }
    },
    threshold: 50,
    enableHapticFeedback: true,
  })

  const { pauseAll } = usePlayerStore()

  // Handle scroll to detect current item
  const handleScroll = useCallback(() => {
    if (!containerRef.current || isScrolling) return

    const container = containerRef.current
    const scrollTop = container.scrollTop
    const itemHeight = container.clientHeight
    const newIndex = Math.round(scrollTop / itemHeight)

    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < items.length) {
      setCurrentIndex(newIndex)
      const item = items[newIndex]
      if (item) {
        onItemChange?.(item, newIndex)

        // Load more items when approaching the end
        if (newIndex >= items.length - 3 && hasMore && !loading) {
          loadMore()
        }
      }
    }
  }, [currentIndex, items, onItemChange, hasMore, loading, loadMore, isScrolling])



  // Setup scroll listener
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    return () => container.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  // Pause all players when scrolling starts
  useEffect(() => {
    if (isScrolling) {
      pauseAll()
    }
  }, [isScrolling, pauseAll])

  // Handle interactions
  const handleInteraction = useCallback((interaction: InteractionData) => {
    onInteraction?.(interaction)
    
    switch (interaction.type) {
      case 'LIKE':
      case 'LOVE':
      case 'FIRE':
      case 'MIND_BLOWN':
      case 'VIBE':
      case 'MOOD_MATCH':
        react(interaction.postId, interaction.type, interaction.mood)
        break
    }
  }, [onInteraction, react])



  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body) return

      switch (event.key) {
        case ' ': // Spacebar
          event.preventDefault()
          // Toggle play/pause for current item
          break
        case 'r':
          event.preventDefault()
          refresh()
          break
        case 'm':
          event.preventDefault()
          clearMoods()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [refresh, clearMoods])

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <p className="text-destructive mb-4">Failed to load feed: {error}</p>
        <Button onClick={refresh} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
      {/* Feed Navigation - TODO: Implement */}
      <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/50 to-transparent">
        <div className="p-4 text-white">
          Feed: {feedType}
        </div>
      </div>

      {/* Mood Selector - TODO: Implement */}
      {selectedMoods.length > 0 && (
        <div className="absolute top-16 left-0 right-0 z-20 bg-gradient-to-b from-black/30 to-transparent">
          <div className="p-4 text-white">
            Moods: {selectedMoods.join(', ')}
          </div>
        </div>
      )}

      {/* Main Feed Container */}
      <div
        ref={(el) => {
          // Use callback ref to assign to multiple refs
          if (el) {
            (containerRef as any).current = el;
            (infiniteScrollHook.containerRef as any).current = el;
            (swipeRef as any).current = el
          }
        }}
        className="feed-container h-full w-full overflow-y-auto scrollbar-hide"
        style={{
          scrollBehavior: 'smooth',
          scrollSnapType: 'y mandatory',
          WebkitOverflowScrolling: 'touch'
        }}
        tabIndex={0}
        role="feed"
        aria-label={`${feedType} feed`}
      >
        {items.map((item, index) => (
          <div
            key={item.post.id}
            ref={(el) => {
              if (el) {
                observeElement(el)
              }
            }}
            data-post-id={item.post.id}
            className="feed-item w-full h-screen flex-shrink-0"
            style={{
              scrollSnapAlign: 'start',
              scrollSnapStop: 'always'
            }}
          >
            <EnhancedContentCard
              item={item}
              isActive={index === currentIndex}
              autoPlay={autoPlay && index === currentIndex}
              onInteraction={handleInteraction}
              onRegisterMedia={registerMediaElement}
              onUnregisterMedia={unregisterMediaElement}
              className="w-full h-full"
            />
          </div>
        ))}

        {/* Loading indicator */}
        {loading && (
          <div
            className="feed-item w-full h-screen flex-shrink-0 flex items-center justify-center bg-black/50"
            style={{
              scrollSnapAlign: 'start',
              scrollSnapStop: 'always'
            }}
          >
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
              <p className="text-white/70">Loading more content...</p>
            </div>
          </div>
        )}

        {/* End of feed indicator */}
        {!hasMore && items.length > 0 && (
          <div
            className="feed-item w-full h-screen flex-shrink-0 flex items-center justify-center bg-gradient-to-b from-black/50 to-purple-900/30"
            style={{
              scrollSnapAlign: 'start',
              scrollSnapStop: 'always'
            }}
          >
            <div className="text-center text-white/70 px-8">
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <Heart className="w-8 h-8 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2">You've reached the end!</h3>
                <p className="text-sm text-white/50 mb-6">
                  You've seen all the latest content in this feed
                </p>
              </div>
              <Button
                onClick={refresh}
                variant="outline"
                size="sm"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                Refresh Feed
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Scroll indicators */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
        <div className="flex flex-col space-y-1">
          {items.slice(Math.max(0, currentIndex - 2), currentIndex + 3).map((_, relativeIndex) => {
            const actualIndex = Math.max(0, currentIndex - 2) + relativeIndex
            const isActive = actualIndex === currentIndex
            
            return (
              <button
                key={actualIndex}
                onClick={() => infiniteScrollHook.scrollToIndex(actualIndex)}
                className={cn(
                  "w-1 h-8 rounded-full transition-all duration-200",
                  isActive 
                    ? "bg-white" 
                    : "bg-white/30 hover:bg-white/50"
                )}
                aria-label={`Go to item ${actualIndex + 1}`}
              />
            )
          })}
        </div>
      </div>

      {/* Performance & Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 left-4 z-20 bg-black/70 backdrop-blur-sm text-white text-xs p-3 rounded-lg space-y-1">
          <div className="font-semibold text-hvppy-400">Feed Debug</div>
          <div>Item: {currentIndex + 1}/{items.length}</div>
          <div>Has More: {hasMore ? 'Yes' : 'No'}</div>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
          <div>Gesturing: {isGesturing ? 'Yes' : 'No'}</div>

          <div className="font-semibold text-hvppy-400 mt-2">Performance</div>
          <div>FPS: {performanceMetrics.fps}</div>
          <div>Memory: {performanceMetrics.memoryUsage}MB</div>
          <div>Smoothness: {performanceMetrics.scrollPerformance.smoothness}%</div>
          <div>Jank: {performanceMetrics.scrollPerformance.jankCount}</div>
          <div>Avg Load: {performanceMetrics.averageLoadTime}ms</div>

          <div className="text-xs text-white/60 mt-2">
            ↑↓ Navigate • Space Play/Pause • M Mute • R Refresh
          </div>
        </div>
      )}
    </div>
  )
}
