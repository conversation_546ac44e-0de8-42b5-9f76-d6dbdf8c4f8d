"use client"

import React, { useEffect, useCallback, useRef, useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { VerticalFeedContainerProps, FeedItem } from '@/types/feed'
import { useFeedStore } from '@/lib/stores/feed-store'
import { useEnhancedInteractionStore } from '@/lib/stores/enhanced-interaction-store'
import { useEnhancedInfiniteScroll } from '@/hooks/feed/use-enhanced-infinite-scroll'
import { useEnhancedMediaAutoplay } from '@/hooks/feed/use-enhanced-media-autoplay'
import { useEnhancedContentInteractions } from '@/hooks/feed/use-enhanced-content-interactions'
import { ImmersiveContentCard } from './immersive-content-card'
import { FeedLoadingState } from './feed-loading-state'
import { FeedErrorState } from './feed-error-state'
import { FeedEmptyState } from './feed-empty-state'
import { FeedPerformanceMonitor } from './feed-performance-monitor'
import { But<PERSON> } from '@/components/ui/button'
import { RefreshCw, Heart, TrendingUp, Zap } from 'lucide-react'

interface ModernVerticalFeedContainerProps extends VerticalFeedContainerProps {
  enableVirtualScrolling?: boolean
  enablePerformanceMonitoring?: boolean
  enableRealTimeUpdates?: boolean
  maxConcurrentPlayers?: number
  customLoadingComponent?: React.ComponentType
  customErrorComponent?: React.ComponentType<{ error: string; onRetry: () => void }>
  customEmptyComponent?: React.ComponentType
}

export function ModernVerticalFeedContainer({
  feedType,
  filters,
  onItemChange,
  onInteraction,
  className,
  autoPlay = true,
  enableVirtualScrolling = true,
  enablePerformanceMonitoring = true,
  enableRealTimeUpdates = true,
  maxConcurrentPlayers = 1,
  customLoadingComponent: CustomLoading,
  customErrorComponent: CustomError,
  customEmptyComponent: CustomEmpty,
  ...props
}: ModernVerticalFeedContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Enhanced store integration
  const {
    items,
    loading,
    error,
    hasMore,
    currentIndex,
    setCurrentIndex,
    virtualScroll,
    metrics,
    loadMore,
    refresh,
    updateFilters,
    updateMetrics,
    getVirtualItems,
  } = useFeedStore()

  const {
    isConnected,
    syncInteractions,
  } = useEnhancedInteractionStore()

  // Enhanced hooks integration
  const {
    containerRef: scrollContainerRef,
    observeElement,
    unobserveElement,
    scrollToIndex,
    getCurrentIndex,
    isScrolling,
    visibleItems,
  } = useEnhancedInfiniteScroll(loadMore, {
    threshold: 0.6,
    onItemChange: (index) => {
      setCurrentIndex(index)
      onItemChange?.(items[index], index)
    },
    preloadDistance: 3,
    offloadDistance: 10,
    enableVirtualization: enableVirtualScrolling,
    itemHeight: virtualScroll.itemHeight,
  })

  const {
    registerMediaElement,
    unregisterMediaElement,
    playMedia,
    pauseMedia,
    getCurrentlyPlaying,
  } = useEnhancedMediaAutoplay({
    threshold: 0.6,
    autoplayDelay: 200,
    pauseDelay: 300,
    enableVideoAutoplay: true,
    enableAudioAutoplay: false,
    muteByDefault: true,
    maxConcurrentPlayers,
  })

  const {
    getInteractionState,
    toggleLike,
    toggleFollow,
    sharePost,
    addComment,
    trackEngagement,
  } = useEnhancedContentInteractions({
    enableOptimisticUpdates: true,
    enableHapticFeedback: true,
    enableAnalytics: true,
  })

  // Memoized virtual items for performance
  const displayItems = useMemo(() => {
    return enableVirtualScrolling ? visibleItems : items
  }, [enableVirtualScrolling, visibleItems, items])

  // Enhanced interaction handler
  const handleInteraction = useCallback((type: string, data: any) => {
    const { postId, mood, content, platform } = data

    switch (type) {
      case 'like':
        toggleLike(postId, mood)
        break
      case 'follow':
        toggleFollow(data.userId)
        break
      case 'share':
        sharePost(postId, platform)
        break
      case 'comment':
        addComment(postId, content, mood)
        break
      case 'view':
        trackEngagement(postId, 'view', data.duration)
        break
      default:
        console.warn('Unknown interaction type:', type)
    }

    onInteraction?.({ type, data, timestamp: new Date() })
  }, [toggleLike, toggleFollow, sharePost, addComment, trackEngagement, onInteraction])

  // Enhanced retry mechanism
  const handleRetry = useCallback(async () => {
    setRetryCount(prev => prev + 1)
    
    try {
      await refresh()
      
      if (enableRealTimeUpdates) {
        await syncInteractions()
      }
    } catch (error) {
      console.error('Retry failed:', error)
    }
  }, [refresh, syncInteractions, enableRealTimeUpdates])

  // Performance optimization: Update filters with debouncing
  const debouncedUpdateFilters = useCallback(
    debounce((newFilters: any) => {
      updateFilters(newFilters)
    }, 300),
    [updateFilters]
  )

  // Initialize feed
  useEffect(() => {
    if (!isInitialized) {
      setIsInitialized(true)
      
      // Apply initial filters
      if (filters) {
        debouncedUpdateFilters(filters)
      }
    }
  }, [isInitialized, filters, debouncedUpdateFilters])

  // Real-time updates
  useEffect(() => {
    if (!enableRealTimeUpdates) return

    const interval = setInterval(() => {
      if (isConnected) {
        syncInteractions()
      }
    }, 10000) // Sync every 10 seconds

    return () => clearInterval(interval)
  }, [enableRealTimeUpdates, isConnected, syncInteractions])

  // Performance monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring) return

    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      updateMetrics({
        loadTime: endTime - startTime
      })
    }
  }, [enablePerformanceMonitoring, updateMetrics])

  // Combine refs for scroll container
  const combinedRef = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      (containerRef as any).current = node;
      (scrollContainerRef as any).current = node
    }
  }, [scrollContainerRef])

  // Loading state
  if (loading && items.length === 0) {
    return (
      <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
        {CustomLoading ? (
          <CustomLoading />
        ) : (
          <FeedLoadingState />
        )}
      </div>
    )
  }

  // Error state
  if (error && items.length === 0) {
    return (
      <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
        {CustomError ? (
          <CustomError error={error} onRetry={handleRetry} />
        ) : (
          <FeedErrorState 
            error={error} 
            onRetry={handleRetry}
            retryCount={retryCount}
          />
        )}
      </div>
    )
  }

  // Empty state
  if (!loading && items.length === 0) {
    return (
      <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
        {CustomEmpty ? (
          <CustomEmpty />
        ) : (
          <FeedEmptyState 
            feedType={feedType}
            onRefresh={handleRetry}
          />
        )}
      </div>
    )
  }

  return (
    <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
      {/* Performance Monitor */}
      {enablePerformanceMonitoring && (
        <FeedPerformanceMonitor
          metrics={metrics}
          isScrolling={isScrolling}
          currentlyPlaying={getCurrentlyPlaying()}
          className="absolute top-4 right-4 z-50"
        />
      )}

      {/* Real-time Status Indicator */}
      {enableRealTimeUpdates && (
        <div className="absolute top-4 left-4 z-50">
          <div className={cn(
            "flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium",
            "backdrop-blur-md border border-white/20",
            isConnected 
              ? "bg-green-500/20 text-green-400 border-green-500/30" 
              : "bg-red-500/20 text-red-400 border-red-500/30"
          )}>
            <div className={cn(
              "w-2 h-2 rounded-full",
              isConnected ? "bg-green-400" : "bg-red-400"
            )} />
            {isConnected ? 'Live' : 'Offline'}
          </div>
        </div>
      )}

      {/* Main Feed Container */}
      <div
        ref={combinedRef}
        className="feed-container h-full w-full overflow-y-auto scrollbar-hide"
        style={{
          scrollBehavior: 'smooth',
          scrollSnapType: 'y mandatory',
          WebkitOverflowScrolling: 'touch'
        }}
        tabIndex={0}
        role="feed"
        aria-label={`${feedType} feed`}
        {...props}
      >
        <AnimatePresence mode="popLayout">
          {displayItems.map((item: FeedItem, index: number) => {
            const globalIndex = enableVirtualScrolling 
              ? virtualScroll.visibleRange.start + index 
              : index
            
            return (
              <motion.div
                key={item.post.id}
                ref={(el) => {
                  if (el) {
                    observeElement(el)
                  }
                }}
                data-post-id={item.post.id}
                className="feed-item w-full h-screen flex-shrink-0"
                style={{
                  scrollSnapAlign: 'start',
                  scrollSnapStop: 'always'
                }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                transition={{ 
                  duration: 0.3,
                  ease: "easeOut"
                }}
                layout
              >
                <ImmersiveContentCard
                  item={item}
                  isActive={globalIndex === currentIndex}
                  isVisible={true}
                  autoPlay={autoPlay && globalIndex === currentIndex}
                  showControls={true}
                  interactionState={getInteractionState(item.post.id)}
                  onInteraction={handleInteraction}
                  onRegisterMedia={registerMediaElement}
                  onUnregisterMedia={unregisterMediaElement}
                  className="w-full h-full"
                />
              </motion.div>
            )
          })}
        </AnimatePresence>

        {/* Loading indicator for infinite scroll */}
        {loading && items.length > 0 && (
          <motion.div 
            className="feed-item w-full h-screen flex-shrink-0 flex items-center justify-center bg-black/50"
            style={{
              scrollSnapAlign: 'start',
              scrollSnapStop: 'always'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p className="text-white/70 text-sm">Loading more content...</p>
            </div>
          </motion.div>
        )}

        {/* End of feed indicator */}
        {!hasMore && items.length > 0 && (
          <motion.div 
            className="feed-item w-full h-screen flex-shrink-0 flex items-center justify-center bg-gradient-to-b from-black/50 to-purple-900/30"
            style={{
              scrollSnapAlign: 'start',
              scrollSnapStop: 'always'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="text-center text-white/70 px-8">
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <Heart className="w-8 h-8 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2">You're all caught up!</h3>
                <p className="text-sm text-white/50 mb-6">
                  You've seen all the latest content in this feed
                </p>
              </div>
              <div className="flex gap-3 justify-center">
                <Button 
                  onClick={handleRetry} 
                  variant="outline" 
                  size="sm"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh Feed
                </Button>
                <Button 
                  onClick={() => scrollToIndex(0)} 
                  variant="outline" 
                  size="sm"
                  className="bg-purple-500/20 border-purple-500/30 text-purple-300 hover:bg-purple-500/30"
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Back to Top
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
