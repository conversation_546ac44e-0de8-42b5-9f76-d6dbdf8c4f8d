"use client"

import React, { useRef, useState, useCallback, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { FeedItem, InteractionType } from '@/types/feed'
import { ContentType } from '@/types'
import { HVPPYContentPlayer } from '@/components/player/hvppy-content-player'
import { FeedVideoPlayer } from '@/components/player/feed-video-player'
import { SocialInteractions } from '@/components/social/social-interactions'
import { FollowButton } from '@/components/social/follow-button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Heart,
  MessageCircle,
  Share2,
  Bookmark,
  MoreHorizontal,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Maximize2,
  Minimize2,
  Verified,
  Clock,
  Eye,
  Sparkles,
  Music
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface InteractionState {
  isLiked: boolean
  isShared: boolean
  isFollowing: boolean
  isSaved: boolean
  likeCount: number
  shareCount: number
  commentCount: number
  viewCount: number
}

interface ImmersiveContentCardProps {
  item: FeedItem
  isActive: boolean
  isVisible: boolean
  autoPlay?: boolean
  showControls?: boolean
  interactionState?: InteractionState
  onInteraction?: (type: string, data: any) => void
  onRegisterMedia?: (element: HTMLVideoElement | HTMLAudioElement, postId: string) => void
  onUnregisterMedia?: (elementId: string) => void
  className?: string
}

export function ImmersiveContentCard({
  item,
  isActive,
  isVisible,
  autoPlay = true,
  showControls = true,
  interactionState,
  onInteraction,
  onRegisterMedia,
  onUnregisterMedia,
  className
}: ImmersiveContentCardProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showUI, setShowUI] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [showComments, setShowComments] = useState(false)
  const [viewStartTime, setViewStartTime] = useState<number | null>(null)

  const { post } = item

  // Track view time
  useEffect(() => {
    if (isActive && isVisible && !viewStartTime) {
      setViewStartTime(Date.now())
    } else if ((!isActive || !isVisible) && viewStartTime) {
      const viewDuration = Date.now() - viewStartTime
      onInteraction?.('view', {
        postId: post.id,
        duration: viewDuration
      })
      setViewStartTime(null)
    }
  }, [isActive, isVisible, viewStartTime, onInteraction, post.id])

  // Auto-hide UI after inactivity
  useEffect(() => {
    if (!showControls) return

    let timeout: NodeJS.Timeout

    const resetTimeout = () => {
      clearTimeout(timeout)
      setShowUI(true)
      timeout = setTimeout(() => setShowUI(false), 3000)
    }

    const handleInteraction = () => resetTimeout()

    resetTimeout()

    const container = containerRef.current
    if (container) {
      container.addEventListener('mousemove', handleInteraction)
      container.addEventListener('touchstart', handleInteraction)
      container.addEventListener('click', handleInteraction)
    }

    return () => {
      clearTimeout(timeout)
      if (container) {
        container.removeEventListener('mousemove', handleInteraction)
        container.removeEventListener('touchstart', handleInteraction)
        container.removeEventListener('click', handleInteraction)
      }
    }
  }, [showControls])

  // Interaction handlers
  const handleLike = useCallback(() => {
    onInteraction?.('like', { postId: post.id })
  }, [onInteraction, post.id])

  const handleShare = useCallback(() => {
    onInteraction?.('share', { postId: post.id })
  }, [onInteraction, post.id])

  const handleComment = useCallback(() => {
    setShowComments(true)
  }, [])

  const handleSave = useCallback(() => {
    onInteraction?.('save', { postId: post.id })
  }, [onInteraction, post.id])

  const handleFollow = useCallback(() => {
    onInteraction?.('follow', {
      userId: post.creator?.userId || post.userId
    })
  }, [onInteraction, post.creator?.userId, post.userId])

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }, [])

  const toggleMute = useCallback(() => {
    setIsMuted(!isMuted)
  }, [isMuted])

  const togglePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying)
  }, [isPlaying])

  // Determine content type and render appropriate player
  const renderContent = () => {
    const isHVPPYContent = post.contentUrl && (
      post.contentUrl.startsWith('hvppy-') ||
      post.contentUrl.startsWith('love-heals-')
    )

    if (isHVPPYContent) {
      return (
        <HVPPYContentPlayer
          contentId={post.contentUrl!}
          className="w-full h-full"
          autoPlay={isActive && autoPlay}
          variant="immersive"
        />
      )
    }

    switch (post.contentType) {
      case ContentType.VIDEO:
        return (
          <FeedVideoPlayer
            contentId={post.id}
            source={{
              url: post.mediaUrls[0] || '',
              type: 'video/mp4'
            }}
            metadata={{
              title: post.title,
              artist: post.creator?.name || post.user.displayName,
              description: post.description,
              tags: post.tags,
              moods: post.moods,
              thumbnail: post.thumbnailUrl,
              likes: interactionState?.likeCount || 0,
              comments: interactionState?.commentCount || 0
            }}
            className="w-full h-full"
            autoPlay={isActive && autoPlay}
            muted={isMuted}
          />
        )

      case ContentType.AUDIO:
        return (
          <div className="relative w-full h-full bg-gradient-to-br from-purple-900/20 to-pink-900/20">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <Music className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-2xl font-bold mb-2">{post.title}</h3>
                <p className="text-white/70">{post.creator?.name || post.user.displayName}</p>
              </div>
            </div>
          </div>
        )

      case ContentType.IMAGE:
        return (
          <div className="relative w-full h-full">
            <img
              src={post.mediaUrls[0] || post.thumbnailUrl || ''}
              alt={post.title || 'Content image'}
              className="w-full h-full object-cover"
            />
          </div>
        )

      case ContentType.TEXT:
        return (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900/20 to-pink-900/20 p-8">
            <div className="text-center text-white max-w-2xl">
              <h3 className="text-3xl font-bold mb-4">{post.title}</h3>
              <p className="text-lg text-white/80 leading-relaxed">{post.content}</p>
            </div>
          </div>
        )

      default:
        return (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900/20 to-pink-900/20">
            <div className="text-center text-white">
              <h3 className="text-2xl font-bold mb-2">{post.title}</h3>
              <p className="text-white/70">{post.description}</p>
            </div>
          </div>
        )
    }
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative w-full h-full bg-black overflow-hidden',
        'cursor-pointer select-none',
        className
      )}
      data-post-id={post.id}
      onClick={() => setShowUI(!showUI)}
    >
      {/* Main Content */}
      <div className="absolute inset-0">
        {renderContent()}
      </div>

      {/* UI Overlays */}
      <AnimatePresence>
        {showUI && showControls && (
          <>
            {/* Top Overlay - Creator Info */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="absolute top-0 left-0 right-0 z-20"
            >
              <div className="bg-gradient-to-b from-black/60 to-transparent p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-10 h-10 border-2 border-white/20">
                      <AvatarImage
                        src={post.creator?.avatarUrl || post.user.avatarUrl}
                        alt={post.creator?.name || post.user.displayName}
                      />
                      <AvatarFallback className="bg-purple-600 text-white">
                        {(post.creator?.name || post.user.displayName || 'U').charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-white font-semibold text-sm">
                          {post.creator?.name || post.user.displayName}
                        </h3>
                        {(post.creator?.user?.isVerified || post.user.isVerified) && (
                          <Verified className="w-4 h-4 text-blue-400" />
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-white/60">
                        <Clock className="w-3 h-3" />
                        <span>{formatDistanceToNow(new Date(post.createdAt), { addSuffix: true })}</span>
                      </div>
                    </div>
                  </div>

                  <FollowButton
                    userId={post.creator?.userId || post.userId}
                    username={post.creator?.name || post.user.username || ''}
                    displayName={post.creator?.name || post.user.displayName || ''}
                    size="sm"
                    variant="outline"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  />
                </div>
              </div>
            </motion.div>

            {/* Bottom Overlay - Content Info & Interactions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="absolute bottom-0 left-0 right-0 z-20"
            >
              <div className="bg-gradient-to-t from-black/60 to-transparent p-4">
                <div className="flex items-end justify-between">
                  {/* Content Metadata */}
                  <div className="flex-1 mr-4">
                    <div className="space-y-2">
                      {post.title && (
                        <h2 className="text-white font-bold text-lg leading-tight">
                          {post.title}
                        </h2>
                      )}

                      {post.description && (
                        <p className="text-white/80 text-sm leading-relaxed line-clamp-2">
                          {post.description}
                        </p>
                      )}

                      {/* Mood Tags */}
                      {post.moods.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {post.moods.slice(0, 3).map((mood) => (
                            <Badge
                              key={mood}
                              variant="secondary"
                              className="bg-white/20 text-white border-0 text-xs"
                            >
                              {mood}
                            </Badge>
                          ))}
                          {post.moods.length > 3 && (
                            <Badge
                              variant="secondary"
                              className="bg-white/20 text-white border-0 text-xs"
                            >
                              +{post.moods.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Stats */}
                      <div className="flex items-center space-x-4 text-xs text-white/60">
                        <div className="flex items-center space-x-1">
                          <Eye className="w-3 h-3" />
                          <span>{interactionState?.viewCount || 0}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Heart className="w-3 h-3" />
                          <span>{interactionState?.likeCount || 0}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span>{interactionState?.commentCount || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Social Interactions */}
                  <div className="flex flex-col items-center space-y-4">
                    <SocialInteractions
                      postId={post.id}
                      likeCount={interactionState?.likeCount || 0}
                      commentCount={interactionState?.commentCount || 0}
                      shareCount={interactionState?.shareCount || 0}
                      variant="vertical"
                      showCounts={true}
                      showLabels={false}
                      onLike={handleLike}
                      onComment={handleComment}
                      onShare={handleShare}
                      className="text-white"
                    />

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSave}
                      className="text-white hover:bg-white/20 p-2"
                    >
                      <Bookmark className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Media Controls */}
            {(post.contentType === ContentType.VIDEO || post.contentType === ContentType.AUDIO) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute bottom-20 left-4 z-30"
              >
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={togglePlayPause}
                    className="bg-black/40 backdrop-blur-sm text-white hover:bg-black/60"
                  >
                    {isPlaying ? (
                      <Pause className="w-4 h-4" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleMute}
                    className="bg-black/40 backdrop-blur-sm text-white hover:bg-black/60"
                  >
                    {isMuted ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </motion.div>
            )}

            {/* Fullscreen Toggle */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute top-4 right-4 z-30"
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="bg-black/40 backdrop-blur-sm text-white hover:bg-black/60"
              >
                {isFullscreen ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </Button>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Comments Modal */}
      <AnimatePresence>
        {showComments && (
          <motion.div
            initial={{ opacity: 0, y: '100%' }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: '100%' }}
            className="absolute inset-0 z-50 bg-black/90 backdrop-blur-md"
          >
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-white/10">
                <h3 className="text-white font-semibold">Comments</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowComments(false)}
                  className="text-white"
                >
                  ✕
                </Button>
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                <div className="text-center text-white/50 mt-8">
                  <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No comments yet</p>
                  <p className="text-sm mt-2">Be the first to comment!</p>
                </div>
              </div>
              <div className="p-4 border-t border-white/10">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    placeholder="Add a comment..."
                    className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50"
                  />
                  <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                    Post
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
