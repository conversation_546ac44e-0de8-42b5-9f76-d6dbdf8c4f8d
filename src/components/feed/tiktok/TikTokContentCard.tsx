"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem, InteractionData } from '@/types/feed'
import { ContentType, ReactionType, InteractionType } from '@/types'
import { TikTokVideoPlayer } from './players/TikTokVideoPlayer'
import { TikTokAudioPlayer } from './players/TikTokAudioPlayer'
import { TikTokImageViewer } from './players/TikTokImageViewer'
import { TikTokTextRenderer } from './players/TikTokTextRenderer'
import { TikTokFeedControls } from './TikTokFeedControls'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { createMediaSource } from '@/lib/player/utils'
import { MediaType } from '@/lib/player/types'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import Link from 'next/link' // Import Link
import { 
  Heart, 
  MessageCircle, 
  Share, 
  Bookmark,
  Music,
  Play,
  MoreHorizontal,
  Sparkles,
  UserPlus,
  Volume2,
  VolumeX
} from 'lucide-react'

interface TikTokContentCardProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onInteraction?: (interaction: InteractionData) => void
  className?: string
  onRegisterMedia?: (element: HTMLVideoElement | HTMLAudioElement, itemId: string, type: 'video' | 'audio') => void
  onUnregisterMedia?: (itemId: string) => void
  preloadedContent?: any
}

export function TikTokContentCard({
  item,
  isActive,
  autoPlay = true,
  onInteraction,
  className,
  onRegisterMedia,
  onUnregisterMedia,
  preloadedContent
}: TikTokContentCardProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLiked, setIsLiked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [showFullDescription, setShowFullDescription] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [showVolumeIndicator, setShowVolumeIndicator] = useState(false)
  
  const { react, unreact, toggleMemory, share } = useContentInteractions()
  const { addToQueue, globalMuted, setGlobalMuted } = usePlayerStore()

  // Check if user has already interacted with this post
  useEffect(() => {
    const userReaction = item.post.reactions.find(r => r.userId === 'current-user') // TODO: Get actual user ID
    setIsLiked(!!userReaction)
    
    const userMemory = item.post.memories.find(m => m.userId === 'current-user') // TODO: Get actual user ID
    setIsSaved(!!userMemory)
    
    // TODO: Check if user is following creator
    setIsFollowing(false)
  }, [item.post.reactions, item.post.memories])

  // Create media source from post content
  const mediaSource = createMediaSource(
    item.post.id,
    item.post.contentUrl || '',
    {
      type: item.post.contentType === ContentType.MUSIC ? MediaType.AUDIO : MediaType.VIDEO,
      format: item.post.contentUrl?.split('.').pop() || 'mp4'
    }
  )

  // Handle social interactions with haptic feedback
  const handleLike = useCallback(async () => {
    try {
      if (isLiked) {
        await unreact(item.post.id)
      } else {
        await react(item.post.id, ReactionType.LIKE)
        // Haptic feedback for like
        if ('vibrate' in navigator) {
          navigator.vibrate(50)
        }
      }
      setIsLiked(!isLiked)
      
      onInteraction?.({
        postId: item.post.id,
        type: InteractionType.LIKE,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Failed to toggle like:', error)
    }
  }, [isLiked, item.post.id, react, unreact, onInteraction])

  const handleSave = useCallback(async () => {
    try {
      await toggleMemory(item.post.id)
      setIsSaved(!isSaved)
      
      // Add to player queue if saved
      if (!isSaved && mediaSource) {
        addToQueue({
          id: item.post.id,
          artist: item.post.creator?.name || item.post.user.displayName || 'Unknown Artist',
          source: mediaSource,
          metadata: {
            id: item.post.id,
            title: item.post.title || 'Untitled',
            artist: item.post.creator?.name || item.post.user.displayName,
            duration: 0, // Will be updated by player
            thumbnail: item.post.thumbnailUrl,
            moods: item.post.moods,
          }
        })
      }
      
      onInteraction?.({
        postId: item.post.id,
        type: InteractionType.MEMORY,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Failed to toggle save:', error)
    }
  }, [isSaved, item.post.id, toggleMemory, onInteraction, mediaSource, addToQueue, item.post])

  const handleShare = useCallback(async () => {
    try {
      await share(item.post.id)
      
      onInteraction?.({
        postId: item.post.id,
        type: InteractionType.SHARE,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Failed to share:', error)
    }
  }, [item.post.id, share, onInteraction])

  const handleFollow = useCallback(async () => {
    try {
      // TODO: Implement follow/unfollow logic
      setIsFollowing(!isFollowing)
      
      if ('vibrate' in navigator) {
        navigator.vibrate(100)
      }
    } catch (error) {
      console.error('Failed to toggle follow:', error)
    }
  }, [isFollowing])

  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted)
    setGlobalMuted(!globalMuted)
    setShowVolumeIndicator(true)
    setTimeout(() => setShowVolumeIndicator(false), 1000)
  }, [isMuted, globalMuted, setGlobalMuted])

  // Render appropriate media player based on content type
  const renderMediaPlayer = () => {
    const baseProps = {
      item,
      isActive,
      autoPlay,
      onUnregisterMedia,
      preloadedContent,
      isMuted,
      onVolumeToggle: handleVolumeToggle,
    }

    switch (item.post.contentType) {
      case ContentType.VIDEO:
        return <TikTokVideoPlayer
          {...baseProps}
          onRegisterMedia={onRegisterMedia as any}
        />
      case ContentType.MUSIC:
        return <TikTokAudioPlayer
          {...baseProps}
          onRegisterMedia={onRegisterMedia as any}
        />
      case ContentType.IMAGE:
        return <TikTokImageViewer
          {...baseProps}
          onRegisterMedia={onRegisterMedia as any}
        />
      case ContentType.TEXT:
      case ContentType.STORY:
        return <TikTokTextRenderer
          {...baseProps}
          onRegisterMedia={onRegisterMedia as any}
        />
      default:
        return <TikTokVideoPlayer
          {...baseProps}
          onRegisterMedia={onRegisterMedia as any}
        />
    }
  }

  // Get mood-based styling
  const getMoodStyling = () => {
    const primaryMood = item.post.moods[0]
    if (!primaryMood) return ''
    
    return `mood-${primaryMood}`
  }

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative w-full h-screen bg-black overflow-hidden",
        "flex flex-col justify-end",
        getMoodStyling(),
        className
      )}
      data-post-id={item.post.id}
    >
      {/* Media Player Background */}
      <div className="absolute inset-0 z-0">
        {renderMediaPlayer()}
      </div>

      {/* Gradient Overlay for Better Text Readability */}
      <div className="absolute inset-0 z-10 bg-gradient-to-t from-black/60 via-transparent to-black/20" />

      {/* Volume Indicator */}
      {showVolumeIndicator && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30">
          <div className="bg-black/70 backdrop-blur-sm rounded-full p-4">
            {isMuted ? (
              <VolumeX className="h-8 w-8 text-white" />
            ) : (
              <Volume2 className="h-8 w-8 text-white" />
            )}
          </div>
        </div>
      )}

      {/* Creator Info - Top Left */}
      <div className="absolute top-4 left-4 z-20 flex items-center space-x-3">
        <Avatar className="h-12 w-12 border-2 border-white">
          <AvatarImage 
            src={item.post.creator?.avatarUrl || item.post.user.avatarUrl} 
            alt={item.post.creator?.name || item.post.user.displayName}
          />
          <AvatarFallback className="bg-hvppy-600 text-white">
            {(item.post.creator?.name || item.post.user.displayName || 'U')[0].toUpperCase()}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="text-white font-semibold text-sm truncate">
              {item.post.creator?.name || item.post.user.displayName}
            </h3>
            {!isFollowing && (
              <Button
                onClick={handleFollow}
                size="sm"
                className="h-6 px-2 bg-hvppy-600 hover:bg-hvppy-700 text-white text-xs"
              >
                <UserPlus className="h-3 w-3 mr-1" />
                Follow
              </Button>
            )}
          </div>
          
          {item.post.creator?.stageName && (
            <p className="text-white/80 text-xs">
              @{item.post.creator.stageName}
            </p>
          )}
        </div>
      </div>

      {/* Content Info - Bottom Left */}
      <div className="absolute bottom-20 left-4 right-20 z-20 space-y-3">
        {/* Title */}
        {item.post.title && (
          <Link href={`/post/${item.post.id}`} className="block">
            <h2 className="text-white font-bold text-lg leading-tight hover:underline">
              {item.post.title}
            </h2>
          </Link>
        )}

        {/* Description */}
        {item.post.description && (
          <div className="text-white text-sm leading-relaxed">
            <p className={cn(
              "transition-all duration-300",
              showFullDescription ? "" : "line-clamp-2"
            )}>
              {item.post.description}
            </p>
            {item.post.description.length > 100 && (
              <button
                onClick={() => setShowFullDescription(!showFullDescription)}
                className="text-white/80 text-xs mt-1 hover:text-white"
              >
                {showFullDescription ? 'Show less' : 'Show more'}
              </button>
            )}
          </div>
        )}

        {/* Mood Tags */}
        {item.post.moods.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {item.post.moods.slice(0, 3).map((mood) => (
              <Badge
                key={mood}
                variant="secondary"
                className={cn(
                  "text-xs px-2 py-1 bg-white/20 text-white border-none",
                  `mood-${mood}`
                )}
              >
                {mood}
              </Badge>
            ))}
            {item.post.moods.length > 3 && (
              <Badge variant="secondary" className="text-xs px-2 py-1 bg-white/20 text-white border-none">
                +{item.post.moods.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Music Info for Audio Content */}
        {item.post.contentType === ContentType.MUSIC && (
          <div className="flex items-center space-x-2 text-white/90 text-sm">
            <Music className="h-4 w-4" />
            <span>Original Audio</span>
          </div>
        )}
      </div>

      {/* Feed Controls - Bottom Right */}
      <div className="absolute bottom-20 right-4 z-20">
        <TikTokFeedControls
          item={item}
          isLiked={isLiked}
          isSaved={isSaved}
          onLike={handleLike}
          onSave={handleSave}
          onShare={handleShare}
          onComment={() => {
            onInteraction?.({
              postId: item.post.id,
              type: InteractionType.COMMENT,
              timestamp: new Date()
            })
          }}
          className="flex flex-col space-y-4"
        />
      </div>

      {/* Tap to Mute/Unmute Overlay */}
      <div 
        className="absolute inset-0 z-15"
        onClick={handleVolumeToggle}
        role="button"
        aria-label={isMuted ? "Unmute" : "Mute"}
      />
    </div>
  )
}