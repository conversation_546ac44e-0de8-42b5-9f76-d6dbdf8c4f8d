"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FeedType } from '@/types/feed'
import { 
  Heart, 
  Compass, 
  TrendingUp, 
  Users, 
  Sparkles, 
  RefreshCw,
  Plus,
  Search
} from 'lucide-react'

interface FeedEmptyStateProps {
  feedType: FeedType
  onRefresh: () => void
  className?: string
}

export function FeedEmptyState({ 
  feedType, 
  onRefresh,
  className 
}: FeedEmptyStateProps) {
  const getEmptyStateConfig = () => {
    switch (feedType) {
      case FeedType.DISCOVER:
        return {
          icon: Compass,
          title: "Ready to Discover?",
          description: "Explore amazing content curated just for you",
          suggestion: "Start by following some creators or adjusting your mood preferences",
          actionText: "Explore Content",
          gradient: "from-purple-500 to-pink-500"
        }
      case FeedType.TRENDING:
        return {
          icon: TrendingUp,
          title: "No Trending Content",
          description: "Be the first to create something amazing",
          suggestion: "Check back later or create content to start the trend",
          actionText: "Create Content",
          gradient: "from-orange-500 to-red-500"
        }
      case FeedType.FOLLOWING:
        return {
          icon: Users,
          title: "Follow Some Creators",
          description: "Your following feed will appear here",
          suggestion: "Discover and follow creators to see their latest content",
          actionText: "Find Creators",
          gradient: "from-blue-500 to-cyan-500"
        }
      case FeedType.LIKED:
        return {
          icon: Heart,
          title: "No Liked Content Yet",
          description: "Content you like will appear here",
          suggestion: "Start exploring and like content you enjoy",
          actionText: "Discover Content",
          gradient: "from-pink-500 to-rose-500"
        }
      default:
        return {
          icon: Sparkles,
          title: "Nothing Here Yet",
          description: "This feed is waiting for amazing content",
          suggestion: "Try refreshing or check back later",
          actionText: "Refresh Feed",
          gradient: "from-purple-500 to-indigo-500"
        }
    }
  }

  const config = getEmptyStateConfig()
  const Icon = config.icon

  return (
    <div className={cn(
      "flex flex-col items-center justify-center h-full bg-gradient-to-br from-gray-900/20 to-gray-800/20 p-8",
      className
    )}>
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 15 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -15, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      {/* Empty State Content */}
      <div className="relative z-10 text-center max-w-md">
        {/* Icon */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 200, 
            damping: 15,
            delay: 0.2 
          }}
          className="mb-8"
        >
          <div className="relative">
            <motion.div
              animate={{ 
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity 
              }}
              className={cn(
                "absolute inset-0 bg-gradient-to-r rounded-full blur-xl opacity-50",
                config.gradient
              )}
            />
            <div className={cn(
              "relative w-20 h-20 mx-auto bg-gradient-to-r rounded-full flex items-center justify-center",
              config.gradient
            )}>
              <Icon className="w-10 h-10 text-white" />
            </div>
          </div>
        </motion.div>

        {/* Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-4 mb-8"
        >
          <h3 className="text-2xl font-bold text-white">
            {config.title}
          </h3>
          <p className="text-white/80 leading-relaxed">
            {config.description}
          </p>
          <p className="text-white/60 text-sm">
            {config.suggestion}
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-3"
        >
          <Button
            onClick={onRefresh}
            className={cn(
              "w-full bg-gradient-to-r text-white font-semibold",
              config.gradient
            )}
            size="lg"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            {config.actionText}
          </Button>
          
          {feedType === FeedType.FOLLOWING && (
            <Button
              onClick={() => {/* TODO: Navigate to discover */}}
              variant="outline"
              className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20"
              size="sm"
            >
              <Search className="w-4 h-4 mr-2" />
              Discover Creators
            </Button>
          )}
          
          {(feedType === FeedType.TRENDING || feedType === FeedType.DISCOVER) && (
            <Button
              onClick={() => {/* TODO: Navigate to content creation */}}
              variant="outline"
              className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20"
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Content
            </Button>
          )}
        </motion.div>

        {/* Decorative Elements */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="mt-12 flex justify-center space-x-8"
        >
          {[Sparkles, Heart, Users].map((DecorIcon, index) => (
            <motion.div
              key={index}
              animate={{
                y: [0, -5, 0],
                rotate: [0, 10, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: index * 0.5,
              }}
              className="text-white/20"
            >
              <DecorIcon className="w-6 h-6" />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  )
}
