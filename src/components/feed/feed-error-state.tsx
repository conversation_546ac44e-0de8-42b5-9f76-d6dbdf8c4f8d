"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { AlertCircle, RefreshCw, Wifi, WifiOff, Zap } from 'lucide-react'

interface FeedErrorStateProps {
  error: string
  onRetry: () => void
  retryCount?: number
  className?: string
}

export function FeedErrorState({ 
  error, 
  onRetry, 
  retryCount = 0,
  className 
}: FeedErrorStateProps) {
  const getErrorIcon = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('connection')) {
      return WifiOff
    }
    if (error.toLowerCase().includes('timeout')) {
      return Zap
    }
    return AlertCircle
  }

  const getErrorMessage = () => {
    if (error.toLowerCase().includes('network')) {
      return {
        title: "Connection Lost",
        description: "Check your internet connection and try again",
        suggestion: "Make sure you're connected to the internet"
      }
    }
    if (error.toLowerCase().includes('timeout')) {
      return {
        title: "Request Timed Out",
        description: "The server is taking too long to respond",
        suggestion: "This might be a temporary issue"
      }
    }
    if (error.toLowerCase().includes('unauthorized')) {
      return {
        title: "Authentication Error",
        description: "Please sign in again to continue",
        suggestion: "Your session may have expired"
      }
    }
    return {
      title: "Something Went Wrong",
      description: error || "An unexpected error occurred",
      suggestion: "Try refreshing the feed"
    }
  }

  const ErrorIcon = getErrorIcon()
  const errorInfo = getErrorMessage()

  return (
    <div className={cn(
      "flex flex-col items-center justify-center h-full bg-gradient-to-br from-red-900/20 to-orange-900/20 p-8",
      className
    )}>
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 10 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-red-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -10, 0],
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Error Content */}
      <div className="relative z-10 text-center max-w-md">
        {/* Error Icon */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 200, 
            damping: 15,
            delay: 0.2 
          }}
          className="mb-6"
        >
          <div className="relative">
            <motion.div
              animate={{ 
                scale: [1, 1.1, 1],
                opacity: [0.5, 0.8, 0.5]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity 
              }}
              className="absolute inset-0 bg-red-500/20 rounded-full blur-xl"
            />
            <div className="relative w-16 h-16 mx-auto bg-red-500/20 rounded-full flex items-center justify-center">
              <ErrorIcon className="w-8 h-8 text-red-400" />
            </div>
          </div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-4 mb-8"
        >
          <h3 className="text-2xl font-bold text-white">
            {errorInfo.title}
          </h3>
          <p className="text-white/80 leading-relaxed">
            {errorInfo.description}
          </p>
          <p className="text-white/60 text-sm">
            {errorInfo.suggestion}
          </p>
          
          {retryCount > 0 && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-orange-400 text-sm"
            >
              Retry attempt: {retryCount}
            </motion.p>
          )}
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-3"
        >
          <Button
            onClick={onRetry}
            className="w-full bg-red-600 hover:bg-red-700 text-white"
            size="lg"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
          
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20"
            size="sm"
          >
            Refresh Page
          </Button>
        </motion.div>

        {/* Technical Details (for development) */}
        {process.env.NODE_ENV === 'development' && (
          <motion.details
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="mt-8 text-left"
          >
            <summary className="text-white/60 text-xs cursor-pointer hover:text-white/80">
              Technical Details
            </summary>
            <div className="mt-2 p-3 bg-black/20 rounded-lg text-xs text-white/70 font-mono">
              <pre className="whitespace-pre-wrap break-words">
                {error}
              </pre>
            </div>
          </motion.details>
        )}
      </div>
    </div>
  )
}
