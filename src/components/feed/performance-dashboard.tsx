"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Activity, 
  Zap, 
  Clock, 
  Wifi, 
  Memory, 
  TrendingUp,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  Gauge
} from 'lucide-react'
import { usePerformanceOptimizer } from '@/hooks/feed/use-performance-optimizer'

interface PerformanceDashboardProps {
  className?: string
  variant?: 'full' | 'compact' | 'minimal'
  showRecommendations?: boolean
  enableAutoOptimization?: boolean
}

export function PerformanceDashboard({
  className,
  variant = 'full',
  showRecommendations = true,
  enableAutoOptimization = true,
}: PerformanceDashboardProps) {
  const [showDetails, setShowDetails] = useState(false)
  
  const {
    metrics,
    isOptimized,
    optimizationLevel,
    enableOptimization,
    disableOptimization,
    getRecommendations,
  } = usePerformanceOptimizer({
    enableOptimizations: enableAutoOptimization,
  })

  const recommendations = getRecommendations()

  const getPerformanceStatus = () => {
    const { fps, memoryUsage, renderTime, scrollPerformance } = metrics
    
    if (fps >= 55 && memoryUsage < 80 && renderTime < 20 && scrollPerformance > 80) {
      return { status: 'excellent', color: 'text-green-500', icon: CheckCircle }
    } else if (fps >= 45 && memoryUsage < 120 && renderTime < 30 && scrollPerformance > 60) {
      return { status: 'good', color: 'text-blue-500', icon: CheckCircle }
    } else if (fps >= 30 && memoryUsage < 150 && renderTime < 50 && scrollPerformance > 40) {
      return { status: 'fair', color: 'text-yellow-500', icon: AlertTriangle }
    } else {
      return { status: 'poor', color: 'text-red-500', icon: XCircle }
    }
  }

  const performanceStatus = getPerformanceStatus()
  const StatusIcon = performanceStatus.icon

  const formatValue = (value: number, unit: string) => {
    if (unit === 'MB') {
      return `${value.toFixed(1)} ${unit}`
    } else if (unit === 'ms') {
      return `${value.toFixed(0)} ${unit}`
    } else if (unit === '%') {
      return `${value.toFixed(0)}${unit}`
    }
    return `${value.toFixed(0)} ${unit}`
  }

  const getProgressColor = (value: number, thresholds: { good: number; fair: number }) => {
    if (value >= thresholds.good) return 'bg-green-500'
    if (value >= thresholds.fair) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <StatusIcon className={cn('h-4 w-4', performanceStatus.color)} />
        <span className="text-sm font-medium">{metrics.fps} FPS</span>
        <Badge variant="outline" className="text-xs">
          {performanceStatus.status}
        </Badge>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              <span className="font-medium">Performance</span>
            </div>
            <StatusIcon className={cn('h-4 w-4', performanceStatus.color)} />
          </div>
          
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div>
              <div className="text-muted-foreground">FPS</div>
              <div className="font-medium">{metrics.fps}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Memory</div>
              <div className="font-medium">{formatValue(metrics.memoryUsage, 'MB')}</div>
            </div>
          </div>
          
          {isOptimized && (
            <Badge variant="secondary" className="mt-2 text-xs">
              Optimized ({optimizationLevel})
            </Badge>
          )}
        </CardContent>
      </Card>
    )
  }

  // Full variant
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Performance Monitor
          </CardTitle>
          <div className="flex items-center gap-2">
            <StatusIcon className={cn('h-5 w-5', performanceStatus.color)} />
            <Badge variant="outline" className="capitalize">
              {performanceStatus.status}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="metrics" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="optimization">Optimization</TabsTrigger>
            <TabsTrigger value="recommendations">Tips</TabsTrigger>
          </TabsList>
          
          <TabsContent value="metrics" className="space-y-4">
            {/* FPS */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Gauge className="h-4 w-4" />
                  <span className="text-sm font-medium">Frame Rate</span>
                </div>
                <span className="text-sm">{formatValue(metrics.fps, 'FPS')}</span>
              </div>
              <Progress 
                value={Math.min(100, (metrics.fps / 60) * 100)} 
                className="h-2"
              />
            </div>

            {/* Memory Usage */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Memory className="h-4 w-4" />
                  <span className="text-sm font-medium">Memory Usage</span>
                </div>
                <span className="text-sm">{formatValue(metrics.memoryUsage, 'MB')}</span>
              </div>
              <Progress 
                value={Math.min(100, (metrics.memoryUsage / 200) * 100)} 
                className="h-2"
              />
            </div>

            {/* Render Time */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm font-medium">Render Time</span>
                </div>
                <span className="text-sm">{formatValue(metrics.renderTime, 'ms')}</span>
              </div>
              <Progress 
                value={Math.max(0, 100 - (metrics.renderTime / 50) * 100)} 
                className="h-2"
              />
            </div>

            {/* Scroll Performance */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="text-sm font-medium">Scroll Performance</span>
                </div>
                <span className="text-sm">{formatValue(metrics.scrollPerformance, '%')}</span>
              </div>
              <Progress 
                value={metrics.scrollPerformance} 
                className="h-2"
              />
            </div>

            {/* Network Latency */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Wifi className="h-4 w-4" />
                  <span className="text-sm font-medium">Network Latency</span>
                </div>
                <span className="text-sm">{formatValue(metrics.networkLatency, 'ms')}</span>
              </div>
              <Progress 
                value={Math.max(0, 100 - (metrics.networkLatency / 1000) * 100)} 
                className="h-2"
              />
            </div>
          </TabsContent>
          
          <TabsContent value="optimization" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Auto-Optimization</span>
                <Badge variant={isOptimized ? "default" : "outline"}>
                  {isOptimized ? `Active (${optimizationLevel})` : 'Disabled'}
                </Badge>
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant={optimizationLevel === 'low' ? "default" : "outline"}
                  size="sm"
                  onClick={() => enableOptimization('low')}
                >
                  Low
                </Button>
                <Button
                  variant={optimizationLevel === 'medium' ? "default" : "outline"}
                  size="sm"
                  onClick={() => enableOptimization('medium')}
                >
                  Medium
                </Button>
                <Button
                  variant={optimizationLevel === 'high' ? "default" : "outline"}
                  size="sm"
                  onClick={() => enableOptimization('high')}
                >
                  High
                </Button>
              </div>
              
              {isOptimized && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={disableOptimization}
                  className="w-full"
                >
                  Disable Optimization
                </Button>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="recommendations" className="space-y-3">
            {showRecommendations && (
              <div className="space-y-2">
                {recommendations.map((recommendation, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-2 p-2 bg-muted/50 rounded text-sm"
                  >
                    <BarChart3 className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <span>{recommendation}</span>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
