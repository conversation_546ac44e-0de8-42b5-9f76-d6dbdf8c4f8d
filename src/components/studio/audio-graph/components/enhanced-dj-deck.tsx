/**
 * EnhancedDJDeck - Complete DJ deck component with all professional features
 * Integrates waveform display, tempo/pitch controls, cue points, and loops
 */

"use client"

import React, { useState, useCallback, useRef } from 'react'
import { cn } from '@/lib/utils'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Play, 
  Pause, 
  Square, 
  SkipBack, 
  SkipForward,
  Upload,
  Repeat,
  Zap,
  Volume2,
  Settings
} from 'lucide-react'

import { WaveformDisplay } from './waveform-display'
import { TempoPitchControl } from './tempo-pitch-control'
import { LoadedTrack, LoadProgress } from '@/lib/audio/track-loader'
import { BeatInfo } from '@/lib/audio/beat-detector'
import { CuePoint } from '@/lib/audio/nodes/dj-deck-audio-node'

export interface EnhancedDJDeckProps {
  deckId: string
  track?: LoadedTrack
  isPlaying: boolean
  currentTime: number
  volume: number
  tempo: number
  pitch: number
  cuePoints: CuePoint[]
  beatInfo?: BeatInfo
  isLooping: boolean
  loopStart?: number
  loopEnd?: number
  onPlay: () => void
  onPause: () => void
  onStop: () => void
  onSeek: (time: number) => void
  onVolumeChange: (volume: number) => void
  onTempoChange: (tempo: number) => void
  onPitchChange: (pitch: number) => void
  onFileLoad: (file: File) => void
  onCuePointAdd: (time: number) => void
  onCuePointRemove: (cueId: string) => void
  onLoopToggle: () => void
  onLoopSet: (start: number, end: number) => void
  onSync?: () => void
  targetBpm?: number
  className?: string
  disabled?: boolean
}

export function EnhancedDJDeck({
  deckId,
  track,
  isPlaying,
  currentTime,
  volume,
  tempo,
  pitch,
  cuePoints,
  beatInfo,
  isLooping,
  loopStart,
  loopEnd,
  onPlay,
  onPause,
  onStop,
  onSeek,
  onVolumeChange,
  onTempoChange,
  onPitchChange,
  onFileLoad,
  onCuePointAdd,
  onCuePointRemove,
  onLoopToggle,
  onLoopSet,
  onSync,
  targetBpm,
  className,
  disabled = false
}: EnhancedDJDeckProps) {
  const [loadProgress, setLoadProgress] = useState<LoadProgress | null>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle file upload
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      onFileLoad(file)
    }
  }, [onFileLoad])

  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file && file.type.startsWith('audio/')) {
      onFileLoad(file)
    }
  }, [onFileLoad])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  // Format time display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Calculate progress percentage
  const progressPercentage = track ? (currentTime / track.audioBuffer.duration) * 100 : 0

  return (
    <div 
      className={cn(
        "bg-card border rounded-lg p-4 space-y-4 min-w-[400px]",
        className
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            Deck {deckId}
          </Badge>
          {beatInfo && (
            <Badge variant="secondary" className="text-xs">
              {Math.round(beatInfo.bpm * tempo)} BPM
            </Badge>
          )}
          {isLooping && (
            <Badge variant="default" className="text-xs">
              <Repeat className="w-3 h-3 mr-1" />
              Loop
            </Badge>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="h-6 w-6 p-0"
        >
          <Settings className="h-3 w-3" />
        </Button>
      </div>

      {/* Track Info */}
      <div className="space-y-2">
        {track ? (
          <div>
            <h3 className="font-medium text-sm truncate">{track.name}</h3>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{formatTime(currentTime)} / {formatTime(track.audioBuffer.duration)}</span>
              <span>{track.metadata.sampleRate}Hz • {track.metadata.channels}ch</span>
            </div>
          </div>
        ) : (
          <div 
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="w-6 h-6 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Drop audio file or click to upload
            </p>
          </div>
        )}
        
        {loadProgress && (
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>{loadProgress.message}</span>
              <span>{loadProgress.progress}%</span>
            </div>
            <Progress value={loadProgress.progress} className="h-1" />
          </div>
        )}
      </div>

      {/* Waveform Display */}
      {track && (
        <WaveformDisplay
          waveformData={track.waveformData}
          duration={track.audioBuffer.duration}
          currentTime={currentTime}
          isPlaying={isPlaying}
          cuePoints={cuePoints}
          onSeek={onSeek}
          onCuePointAdd={onCuePointAdd}
          onCuePointRemove={onCuePointRemove}
          height={60}
          className="border rounded"
        />
      )}

      {/* Transport Controls */}
      <div className="flex items-center justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onStop}
          disabled={disabled || !track}
          className="h-8 w-8 p-0"
        >
          <Square className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onSeek(Math.max(0, currentTime - 10))}
          disabled={disabled || !track}
          className="h-8 w-8 p-0"
        >
          <SkipBack className="h-4 w-4" />
        </Button>
        
        <Button
          variant={isPlaying ? "default" : "outline"}
          size="sm"
          onClick={isPlaying ? onPause : onPlay}
          disabled={disabled || !track}
          className="h-10 w-10 p-0"
        >
          {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onSeek(Math.min(track?.audioBuffer.duration || 0, currentTime + 10))}
          disabled={disabled || !track}
          className="h-8 w-8 p-0"
        >
          <SkipForward className="h-4 w-4" />
        </Button>
        
        <Button
          variant={isLooping ? "default" : "outline"}
          size="sm"
          onClick={onLoopToggle}
          disabled={disabled || !track}
          className="h-8 w-8 p-0"
        >
          <Repeat className="h-4 w-4" />
        </Button>
      </div>

      {/* Volume Control */}
      <div className="flex items-center gap-2">
        <Volume2 className="h-4 w-4 text-muted-foreground" />
        <input
          type="range"
          min="0"
          max="1"
          step="0.01"
          value={volume}
          onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
          disabled={disabled}
          className="flex-1"
        />
        <span className="text-xs text-muted-foreground w-8">
          {Math.round(volume * 100)}%
        </span>
      </div>

      {/* Advanced Controls */}
      {showAdvanced && (
        <div className="space-y-4 pt-4 border-t">
          <TempoPitchControl
            tempo={tempo}
            pitch={pitch}
            bpm={beatInfo?.bpm}
            targetBpm={targetBpm}
            onTempoChange={onTempoChange}
            onPitchChange={onPitchChange}
            onTempoReset={() => onTempoChange(1.0)}
            onPitchReset={() => onPitchChange(0)}
            onSync={onSync}
            disabled={disabled || !track}
          />
          
          {/* Cue Points */}
          {cuePoints.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Cue Points</h4>
              <div className="flex flex-wrap gap-1">
                {cuePoints.map((cue, index) => (
                  <Button
                    key={cue.id}
                    variant="outline"
                    size="sm"
                    onClick={() => onSeek(cue.time)}
                    className="h-6 px-2 text-xs"
                  >
                    {cue.label || `Cue ${index + 1}`}
                  </Button>
                ))}
              </div>
            </div>
          )}
          
          {/* Sync Controls */}
          {onSync && targetBpm && (
            <div className="flex items-center justify-between">
              <span className="text-sm">Sync to {targetBpm} BPM</span>
              <Button
                variant="outline"
                size="sm"
                onClick={onSync}
                disabled={disabled || !track || !beatInfo}
                className="h-6 px-2"
              >
                <Zap className="h-3 w-3 mr-1" />
                Sync
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        onChange={handleFileUpload}
        className="hidden"
      />
    </div>
  )
}
