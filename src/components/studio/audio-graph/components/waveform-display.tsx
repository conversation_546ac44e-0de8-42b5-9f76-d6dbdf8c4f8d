/**
 * WaveformDisplay - Real-time waveform visualization component
 * Shows audio waveform with playback position, cue points, and interactive seeking
 */

"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { CuePoint } from '@/lib/audio/nodes/dj-deck-audio-node'

export interface WaveformDisplayProps {
  waveformData?: Float32Array
  duration: number
  currentTime: number
  isPlaying: boolean
  cuePoints?: CuePoint[]
  className?: string
  height?: number
  onSeek?: (time: number) => void
  onCuePointAdd?: (time: number) => void
  onCuePointRemove?: (cueId: string) => void
  showGrid?: boolean
  showTimeLabels?: boolean
  waveformColor?: string
  progressColor?: string
  backgroundColor?: string
}

export function WaveformDisplay({
  waveformData,
  duration,
  currentTime,
  isPlaying,
  cuePoints = [],
  className,
  height = 80,
  onSeek,
  onCuePointAdd,
  onCuePointRemove: _onCuePointRemove,
  showGrid = true,
  showTimeLabels = true,
  waveformColor = '#8b5cf6',
  progressColor = '#a855f7',
  backgroundColor = '#1a1a1a'
}: WaveformDisplayProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [canvasSize, setCanvasSize] = useState({ width: 0, height })
  const [isDragging, setIsDragging] = useState(false)
  const [hoveredTime, setHoveredTime] = useState<number | null>(null)

  // Update canvas size on container resize
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        setCanvasSize({ width: rect.width, height })
      }
    }

    updateSize()
    window.addEventListener('resize', updateSize)
    return () => window.removeEventListener('resize', updateSize)
  }, [height])

  // Draw waveform
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas || !waveformData || canvasSize.width === 0) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const { width, height } = canvasSize
    const dpr = window.devicePixelRatio || 1

    // Set canvas size for high DPI displays
    canvas.width = width * dpr
    canvas.height = height * dpr
    canvas.style.width = `${width}px`
    canvas.style.height = `${height}px`
    ctx.scale(dpr, dpr)

    // Clear canvas
    ctx.fillStyle = backgroundColor
    ctx.fillRect(0, 0, width, height)

    // Draw grid if enabled
    if (showGrid) {
      drawGrid(ctx, width, height)
    }

    // Draw waveform
    drawWaveformBars(ctx, width, height)

    // Draw progress
    drawProgress(ctx, width, height)

    // Draw cue points
    drawCuePoints(ctx, width, height)

    // Draw playhead
    drawPlayhead(ctx, width, height)

    // Draw time labels if enabled
    if (showTimeLabels) {
      drawTimeLabels(ctx, width, height)
    }

    // Draw hover indicator
    if (hoveredTime !== null) {
      drawHoverIndicator(ctx, width, height)
    }
  }, [
    waveformData,
    canvasSize,
    currentTime,
    duration,
    cuePoints,
    hoveredTime,
    showGrid,
    showTimeLabels,
    waveformColor,
    progressColor,
    backgroundColor
  ])

  // Draw grid lines
  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.strokeStyle = '#333'
    ctx.lineWidth = 0.5

    // Vertical grid lines (time markers)
    const timeStep = duration > 60 ? 10 : 5 // 10s or 5s intervals
    const steps = Math.floor(duration / timeStep)
    
    for (let i = 1; i <= steps; i++) {
      const x = (i * timeStep / duration) * width
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, height)
      ctx.stroke()
    }

    // Horizontal center line
    ctx.beginPath()
    ctx.moveTo(0, height / 2)
    ctx.lineTo(width, height / 2)
    ctx.stroke()
  }

  // Draw waveform bars
  const drawWaveformBars = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!waveformData) return

    const barWidth = width / waveformData.length
    const centerY = height / 2
    const maxBarHeight = height * 0.4

    ctx.fillStyle = waveformColor

    for (let i = 0; i < waveformData.length; i++) {
      const amplitude = waveformData[i]
      const barHeight = amplitude * maxBarHeight
      const x = i * barWidth
      const y = centerY - barHeight / 2

      ctx.fillRect(x, y, Math.max(1, barWidth - 0.5), barHeight)
    }
  }

  // Draw progress overlay
  const drawProgress = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!waveformData || duration === 0) return

    // const progressWidth = (currentTime / duration) * width
    const barWidth = width / waveformData.length
    const centerY = height / 2
    const maxBarHeight = height * 0.4

    ctx.fillStyle = progressColor

    const progressBars = Math.floor((currentTime / duration) * waveformData.length)
    
    for (let i = 0; i < progressBars; i++) {
      const amplitude = waveformData[i]
      const barHeight = amplitude * maxBarHeight
      const x = i * barWidth
      const y = centerY - barHeight / 2

      ctx.fillRect(x, y, Math.max(1, barWidth - 0.5), barHeight)
    }
  }

  // Draw cue points
  const drawCuePoints = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    cuePoints.forEach(cue => {
      const x = (cue.time / duration) * width
      
      ctx.strokeStyle = cue.color || '#f59e0b'
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, height)
      ctx.stroke()

      // Draw cue point marker
      ctx.fillStyle = cue.color || '#f59e0b'
      ctx.beginPath()
      ctx.arc(x, 8, 4, 0, Math.PI * 2)
      ctx.fill()

      // Draw label if provided
      if (cue.label) {
        ctx.fillStyle = '#fff'
        ctx.font = '10px sans-serif'
        ctx.fillText(cue.label, x + 6, 12)
      }
    })
  }

  // Draw playhead
  const drawPlayhead = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (duration === 0) return

    const x = (currentTime / duration) * width
    
    ctx.strokeStyle = isPlaying ? '#ef4444' : '#6b7280'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, height)
    ctx.stroke()

    // Draw playhead triangle
    ctx.fillStyle = isPlaying ? '#ef4444' : '#6b7280'
    ctx.beginPath()
    ctx.moveTo(x - 4, 0)
    ctx.lineTo(x + 4, 0)
    ctx.lineTo(x, 8)
    ctx.closePath()
    ctx.fill()
  }

  // Draw time labels
  const drawTimeLabels = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.fillStyle = '#9ca3af'
    ctx.font = '10px monospace'
    ctx.textAlign = 'center'

    const timeStep = duration > 60 ? 10 : 5
    const steps = Math.floor(duration / timeStep)
    
    for (let i = 0; i <= steps; i++) {
      const time = i * timeStep
      const x = (time / duration) * width
      const label = formatTime(time)
      
      ctx.fillText(label, x, height - 4)
    }

    // Current time
    const currentX = (currentTime / duration) * width
    ctx.fillStyle = '#fff'
    ctx.fillText(formatTime(currentTime), currentX, 12)
  }

  // Draw hover indicator
  const drawHoverIndicator = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (hoveredTime === null) return

    const x = (hoveredTime / duration) * width
    
    ctx.strokeStyle = '#64748b'
    ctx.lineWidth = 1
    ctx.setLineDash([2, 2])
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, height)
    ctx.stroke()
    ctx.setLineDash([])

    // Time tooltip
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    ctx.fillRect(x - 20, height - 20, 40, 16)
    ctx.fillStyle = '#fff'
    ctx.font = '10px monospace'
    ctx.textAlign = 'center'
    ctx.fillText(formatTime(hoveredTime), x, height - 8)
  }

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Handle mouse events
  const handleMouseMove = (event: React.MouseEvent) => {
    if (!containerRef.current || duration === 0) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const time = (x / rect.width) * duration
    
    setHoveredTime(Math.max(0, Math.min(duration, time)))

    if (isDragging && onSeek) {
      onSeek(time)
    }
  }

  const handleMouseDown = (event: React.MouseEvent) => {
    if (!containerRef.current || duration === 0) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const time = (x / rect.width) * duration
    
    setIsDragging(true)
    onSeek?.(Math.max(0, Math.min(duration, time)))
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleMouseLeave = () => {
    setHoveredTime(null)
    setIsDragging(false)
  }

  const handleDoubleClick = (event: React.MouseEvent) => {
    if (!containerRef.current || duration === 0) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const time = (x / rect.width) * duration
    
    onCuePointAdd?.(Math.max(0, Math.min(duration, time)))
  }

  // Redraw when dependencies change
  useEffect(() => {
    drawWaveform()
  }, [drawWaveform])

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full cursor-crosshair select-none",
        className
      )}
      style={{ height }}
      onMouseMove={handleMouseMove}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      onDoubleClick={handleDoubleClick}
    >
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
      />
      
      {!waveformData && (
        <div className="absolute inset-0 flex items-center justify-center text-muted-foreground text-sm">
          No audio loaded
        </div>
      )}
    </div>
  )
}
