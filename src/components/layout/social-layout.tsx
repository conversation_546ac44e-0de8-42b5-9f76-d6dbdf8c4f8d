"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Notifications } from '@/components/social/notifications'
import { UserSuggestions } from '@/components/social/user-suggestions'
import { 
  Home, 
  Compass, 
  TrendingUp, 
  Heart, 
  Users, 
  Bell,
  Plus,
  Search,
  Settings,
  Sparkles,
  User,
  LogOut,
  Moon,
  Sun,
  Bookmark,
  BarChart3,
  MessageCircle,
  Menu,
  X
} from 'lucide-react'
import { useSocialStore, selectUnreadCount, selectCurrentUser } from '@/lib/stores/social-store'
import { useTheme } from 'next-themes'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'

interface SocialLayoutProps {
  children: React.ReactNode
  className?: string
  showSidebar?: boolean
  showHeader?: boolean
  sidebarCollapsed?: boolean
}

const navigationItems = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    href: '/social',
    badge: null,
  },
  {
    id: 'discover',
    label: 'Discover',
    icon: Compass,
    href: '/social/discover',
    badge: null,
  },
  {
    id: 'trending',
    label: 'Trending',
    icon: TrendingUp,
    href: '/social/trending',
    badge: 'Hot',
  },
  {
    id: 'following',
    label: 'Following',
    icon: Users,
    href: '/social/following',
    badge: null,
  },
  {
    id: 'saved',
    label: 'Saved',
    icon: Bookmark,
    href: '/social/saved',
    badge: null,
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: BarChart3,
    href: '/social/analytics',
    badge: null,
  },
]

export function SocialLayout({
  children,
  className,
  showSidebar = true,
  showHeader = true,
  sidebarCollapsed: initialCollapsed = false,
}: SocialLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(initialCollapsed)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showMobileMenu, setShowMobileMenu] = useState(false)
  const [activeItem, setActiveItem] = useState('home')
  
  const unreadCount = useSocialStore(selectUnreadCount)
  const currentUser = useSocialStore(selectCurrentUser)
  const { theme, setTheme } = useTheme()

  // Close mobile menu on route change
  useEffect(() => {
    setShowMobileMenu(false)
  }, [activeItem])

  const handleSignOut = () => {
    // Implement sign out logic
    console.log('Sign out')
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      {showHeader && (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            {/* Logo & Mobile Menu */}
            <div className="flex items-center gap-4">
              {showSidebar && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  onClick={() => setShowMobileMenu(!showMobileMenu)}
                >
                  {showMobileMenu ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
                </Button>
              )}
              
              <Link href="/" className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <Heart className="h-4 w-4 text-white" />
                </div>
                <span className="font-bold text-xl hidden sm:block">HVPPY</span>
              </Link>
            </div>

            {/* Search */}
            <div className="flex-1 max-w-md mx-8 hidden md:block">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search creators, content, moods..."
                  className="w-full pl-10 pr-4 py-2 border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" asChild className="hidden sm:flex">
                <Link href="/studio">
                  <Plus className="h-4 w-4 mr-2" />
                  Create
                </Link>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="relative"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <Bell className="h-4 w-4" />
                {unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs"
                  >
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </Button>

              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={currentUser?.avatarUrl} alt={currentUser?.displayName} />
                      <AvatarFallback>
                        {(currentUser?.displayName || 'U').charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="hidden sm:inline">{currentUser?.displayName}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="gap-2">
                      <User className="h-4 w-4" />
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings" className="gap-2">
                      <Settings className="h-4 w-4" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
                    {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                    <span className="ml-2">{theme === 'dark' ? 'Light' : 'Dark'} Mode</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                    <LogOut className="h-4 w-4" />
                    <span className="ml-2">Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Notifications Dropdown */}
        <AnimatePresence>
          {showNotifications && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full right-4 z-50 mt-2"
            >
              <Notifications
                variant="dropdown"
                maxHeight="400px"
                showHeader={true}
              />
            </motion.div>
          )}
        </AnimatePresence>
        </header>
      )}

      <div className="flex">
        {/* Sidebar */}
        {showSidebar && (
          <>
            {/* Desktop Sidebar */}
            <aside className={cn(
              'hidden md:flex flex-col border-r bg-background transition-all duration-300',
              sidebarCollapsed ? 'w-16' : 'w-64'
            )}>
              <div className="p-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                  className="w-full justify-start gap-2"
                >
                  <Menu className="h-4 w-4" />
                  {!sidebarCollapsed && <span>Collapse</span>}
                </Button>
              </div>

              <nav className="flex-1 px-4 space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon
                  const isActive = activeItem === item.id
                  
                  return (
                    <Button
                      key={item.id}
                      variant={isActive ? "default" : "ghost"}
                      size="sm"
                      asChild
                      className={cn(
                        'w-full justify-start gap-3',
                        sidebarCollapsed && 'justify-center'
                      )}
                      onClick={() => setActiveItem(item.id)}
                    >
                      <Link href={item.href}>
                        <Icon className="h-4 w-4" />
                        {!sidebarCollapsed && (
                          <>
                            <span>{item.label}</span>
                            {item.badge && (
                              <Badge variant="secondary" className="ml-auto text-xs">
                                {item.badge}
                              </Badge>
                            )}
                          </>
                        )}
                      </Link>
                    </Button>
                  )
                })}
              </nav>

              {!sidebarCollapsed && (
                <div className="p-4 border-t">
                  <UserSuggestions
                    title="Suggested"
                    limit={3}
                    variant="list"
                    showStats={false}
                  />
                </div>
              )}
            </aside>

            {/* Mobile Sidebar */}
            <AnimatePresence>
              {showMobileMenu && (
                <>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 z-40 bg-black/50 md:hidden"
                    onClick={() => setShowMobileMenu(false)}
                  />
                  <motion.aside
                    initial={{ x: -300 }}
                    animate={{ x: 0 }}
                    exit={{ x: -300 }}
                    className="fixed left-0 top-16 bottom-0 z-50 w-64 bg-background border-r md:hidden"
                  >
                    <nav className="p-4 space-y-2">
                      {navigationItems.map((item) => {
                        const Icon = item.icon
                        const isActive = activeItem === item.id
                        
                        return (
                          <Button
                            key={item.id}
                            variant={isActive ? "default" : "ghost"}
                            size="sm"
                            asChild
                            className="w-full justify-start gap-3"
                            onClick={() => setActiveItem(item.id)}
                          >
                            <Link href={item.href}>
                              <Icon className="h-4 w-4" />
                              <span>{item.label}</span>
                              {item.badge && (
                                <Badge variant="secondary" className="ml-auto text-xs">
                                  {item.badge}
                                </Badge>
                              )}
                            </Link>
                          </Button>
                        )
                      })}
                    </nav>
                  </motion.aside>
                </>
              )}
            </AnimatePresence>
          </>
        )}

        {/* Main Content */}
        <main className={cn('flex-1 min-h-screen', className)}>
          {children}
        </main>
      </div>
    </div>
  )
}
