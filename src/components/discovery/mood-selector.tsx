"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { 
  <PERSON><PERSON>les, 
  Heart, 
  Zap, 
  Sun, 
  Moon, 
  Coffee,
  Music,
  <PERSON><PERSON>,
  <PERSON>,
  Smile,
  <PERSON>own,
  Meh,
  X,
  Shuffle,
  TrendingUp,
  Clock
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface Mood {
  id: string
  name: string
  icon: React.ComponentType<any>
  color: string
  gradient: string
  description: string
  intensity: number
  category: 'energy' | 'emotion' | 'creativity' | 'social'
}

interface MoodSelectorProps {
  selectedMoods: string[]
  onMoodChange: (moods: string[]) => void
  maxSelections?: number
  variant?: 'compact' | 'expanded' | 'floating'
  showIntensity?: boolean
  showTrending?: boolean
  className?: string
}

const moods: Mood[] = [
  {
    id: 'happy',
    name: 'Happy',
    icon: Smile,
    color: 'text-yellow-500',
    gradient: 'from-yellow-400 to-orange-500',
    description: 'Joyful and uplifting content',
    intensity: 80,
    category: 'emotion'
  },
  {
    id: 'energetic',
    name: 'Energetic',
    icon: Zap,
    color: 'text-orange-500',
    gradient: 'from-orange-400 to-red-500',
    description: 'High-energy and motivating',
    intensity: 90,
    category: 'energy'
  },
  {
    id: 'chill',
    name: 'Chill',
    icon: Moon,
    color: 'text-blue-500',
    gradient: 'from-blue-400 to-purple-500',
    description: 'Relaxed and peaceful vibes',
    intensity: 30,
    category: 'energy'
  },
  {
    id: 'creative',
    name: 'Creative',
    icon: Palette,
    color: 'text-purple-500',
    gradient: 'from-purple-400 to-pink-500',
    description: 'Artistic and inspiring content',
    intensity: 70,
    category: 'creativity'
  },
  {
    id: 'romantic',
    name: 'Romantic',
    icon: Heart,
    color: 'text-pink-500',
    gradient: 'from-pink-400 to-rose-500',
    description: 'Love and relationship content',
    intensity: 60,
    category: 'emotion'
  },
  {
    id: 'nostalgic',
    name: 'Nostalgic',
    icon: Clock,
    color: 'text-amber-500',
    gradient: 'from-amber-400 to-yellow-500',
    description: 'Memories and throwback vibes',
    intensity: 50,
    category: 'emotion'
  },
  {
    id: 'mysterious',
    name: 'Mysterious',
    icon: Brain,
    color: 'text-indigo-500',
    gradient: 'from-indigo-400 to-purple-600',
    description: 'Intriguing and thought-provoking',
    intensity: 40,
    category: 'creativity'
  },
  {
    id: 'social',
    name: 'Social',
    icon: Coffee,
    color: 'text-green-500',
    gradient: 'from-green-400 to-teal-500',
    description: 'Community and connection focused',
    intensity: 75,
    category: 'social'
  }
]

const trendingMoods = ['happy', 'energetic', 'creative', 'chill']

export function MoodSelector({
  selectedMoods,
  onMoodChange,
  maxSelections = 3,
  variant = 'expanded',
  showIntensity = false,
  showTrending = true,
  className
}: MoodSelectorProps) {
  const [intensityFilter, setIntensityFilter] = useState([0, 100])
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
  const [isAnimating, setIsAnimating] = useState(false)

  const filteredMoods = moods.filter(mood => {
    const intensityMatch = mood.intensity >= intensityFilter[0] && mood.intensity <= intensityFilter[1]
    const categoryMatch = !categoryFilter || mood.category === categoryFilter
    return intensityMatch && categoryMatch
  })

  const handleMoodToggle = (moodId: string) => {
    setIsAnimating(true)
    
    if (selectedMoods.includes(moodId)) {
      onMoodChange(selectedMoods.filter(id => id !== moodId))
    } else if (selectedMoods.length < maxSelections) {
      onMoodChange([...selectedMoods, moodId])
    }
    
    setTimeout(() => setIsAnimating(false), 300)
  }

  const handleRandomSelection = () => {
    const randomMoods = moods
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.min(maxSelections, 2))
      .map(mood => mood.id)
    
    onMoodChange(randomMoods)
  }

  const clearSelection = () => {
    onMoodChange([])
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex flex-wrap gap-2', className)}>
        {selectedMoods.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSelection}
            className="h-8 px-2"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
        {filteredMoods.slice(0, 6).map((mood) => {
          const Icon = mood.icon
          const isSelected = selectedMoods.includes(mood.id)
          
          return (
            <motion.div
              key={mood.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant={isSelected ? "default" : "outline"}
                size="sm"
                onClick={() => handleMoodToggle(mood.id)}
                className={cn(
                  'h-8 gap-1.5 transition-all duration-200',
                  isSelected && `bg-gradient-to-r ${mood.gradient} text-white border-0`
                )}
                disabled={!isSelected && selectedMoods.length >= maxSelections}
              >
                <Icon className="h-3 w-3" />
                <span className="text-xs">{mood.name}</span>
              </Button>
            </motion.div>
          )
        })}
      </div>
    )
  }

  if (variant === 'floating') {
    return (
      <Card className={cn('absolute top-4 right-4 z-50 w-64', className)}>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm">Mood Filter</h3>
              {selectedMoods.length > 0 && (
                <Button variant="ghost" size="sm" onClick={clearSelection}>
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              {filteredMoods.slice(0, 6).map((mood) => {
                const Icon = mood.icon
                const isSelected = selectedMoods.includes(mood.id)
                
                return (
                  <Button
                    key={mood.id}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleMoodToggle(mood.id)}
                    className={cn(
                      'h-10 gap-2 text-xs',
                      isSelected && `bg-gradient-to-r ${mood.gradient} text-white border-0`
                    )}
                    disabled={!isSelected && selectedMoods.length >= maxSelections}
                  >
                    <Icon className="h-3 w-3" />
                    {mood.name}
                  </Button>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Expanded variant (default)
  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-500" />
              <h3 className="font-semibold">Choose Your Mood</h3>
              <Badge variant="outline" className="text-xs">
                {selectedMoods.length}/{maxSelections}
              </Badge>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRandomSelection}
                className="gap-2"
              >
                <Shuffle className="h-3 w-3" />
                Random
              </Button>
              {selectedMoods.length > 0 && (
                <Button variant="ghost" size="sm" onClick={clearSelection}>
                  Clear
                </Button>
              )}
            </div>
          </div>

          {/* Trending Moods */}
          {showTrending && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <TrendingUp className="h-4 w-4" />
                Trending Now
              </div>
              <div className="flex flex-wrap gap-2">
                {trendingMoods.map((moodId) => {
                  const mood = moods.find(m => m.id === moodId)
                  if (!mood) return null
                  
                  const Icon = mood.icon
                  const isSelected = selectedMoods.includes(mood.id)
                  
                  return (
                    <motion.div
                      key={mood.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant={isSelected ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleMoodToggle(mood.id)}
                        className={cn(
                          'gap-2 transition-all duration-200',
                          isSelected && `bg-gradient-to-r ${mood.gradient} text-white border-0`
                        )}
                        disabled={!isSelected && selectedMoods.length >= maxSelections}
                      >
                        <Icon className="h-3 w-3" />
                        {mood.name}
                        <Badge variant="secondary" className="text-xs ml-1">
                          Hot
                        </Badge>
                      </Button>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Category Filters */}
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Categories</div>
            <div className="flex flex-wrap gap-2">
              {['energy', 'emotion', 'creativity', 'social'].map((category) => (
                <Button
                  key={category}
                  variant={categoryFilter === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCategoryFilter(categoryFilter === category ? null : category)}
                  className="capitalize"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Intensity Filter */}
          {showIntensity && (
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                Intensity: {intensityFilter[0]}% - {intensityFilter[1]}%
              </div>
              <Slider
                value={intensityFilter}
                onValueChange={setIntensityFilter}
                max={100}
                min={0}
                step={10}
                className="w-full"
              />
            </div>
          )}

          {/* Mood Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <AnimatePresence>
              {filteredMoods.map((mood) => {
                const Icon = mood.icon
                const isSelected = selectedMoods.includes(mood.id)
                const isTrending = trendingMoods.includes(mood.id)
                
                return (
                  <motion.div
                    key={mood.id}
                    layout
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="relative"
                  >
                    <Button
                      variant={isSelected ? "default" : "outline"}
                      onClick={() => handleMoodToggle(mood.id)}
                      disabled={!isSelected && selectedMoods.length >= maxSelections}
                      className={cn(
                        'w-full h-20 flex-col gap-2 transition-all duration-300',
                        isSelected && `bg-gradient-to-br ${mood.gradient} text-white border-0 shadow-lg`,
                        !isSelected && 'hover:shadow-md'
                      )}
                    >
                      <Icon className={cn(
                        'h-5 w-5 transition-colors',
                        isSelected ? 'text-white' : mood.color
                      )} />
                      <span className="text-xs font-medium">{mood.name}</span>
                      
                      {showIntensity && (
                        <div className="absolute bottom-1 right-1">
                          <Badge variant="secondary" className="text-xs px-1">
                            {mood.intensity}%
                          </Badge>
                        </div>
                      )}
                      
                      {isTrending && (
                        <div className="absolute top-1 right-1">
                          <TrendingUp className="h-3 w-3 text-orange-500" />
                        </div>
                      )}
                    </Button>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>

          {/* Selected Moods Summary */}
          {selectedMoods.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Selected Moods</div>
              <div className="flex flex-wrap gap-2">
                {selectedMoods.map((moodId) => {
                  const mood = moods.find(m => m.id === moodId)
                  if (!mood) return null
                  
                  return (
                    <Badge
                      key={mood.id}
                      variant="secondary"
                      className={cn(
                        'gap-1 cursor-pointer hover:opacity-80',
                        `bg-gradient-to-r ${mood.gradient} text-white`
                      )}
                      onClick={() => handleMoodToggle(mood.id)}
                    >
                      {mood.name}
                      <X className="h-3 w-3" />
                    </Badge>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
