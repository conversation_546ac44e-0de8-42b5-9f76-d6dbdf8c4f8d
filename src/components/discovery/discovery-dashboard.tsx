"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { MoodSelector } from './mood-selector'
import {
  Sparkles,
  TrendingUp,
  Users,
  Brain,
  Clock,
  BarChart3,
  Settings,
  RefreshCw,
  Filter,
  Eye,
  Heart,
  MessageCircle,
  Share2,
  Zap,
  Target,
  Shuffle
} from 'lucide-react'
import { useContentDiscovery } from '@/hooks/discovery/use-content-discovery'
import { motion, AnimatePresence } from 'framer-motion'

interface DiscoveryDashboardProps {
  className?: string
  onContentSelect?: (content: any[]) => void
  showAdvancedControls?: boolean
}

export function DiscoveryDashboard({
  className,
  onContentSelect,
  showAdvancedControls = true,
}: DiscoveryDashboardProps) {
  const [selectedMoods, setSelectedMoods] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState('personalized')
  const [showAlgorithmSettings, setShowAlgorithmSettings] = useState(false)
  
  const {
    discoverContent,
    getTrendingContent,
    getPersonalizedFeed,
    algorithms,
    updateAlgorithmWeight,
    toggleAlgorithm,
    getDiscoveryMetrics,
    isLoading,
    lastUpdated,
  } = useContentDiscovery()

  const metrics = getDiscoveryMetrics()

  const handleDiscoverContent = async () => {
    try {
      let content = []
      
      switch (activeTab) {
        case 'mood':
          content = await discoverContent(selectedMoods)
          break
        case 'trending':
          content = await getTrendingContent()
          break
        case 'personalized':
        default:
          content = await getPersonalizedFeed()
          break
      }
      
      if (onContentSelect) {
        onContentSelect(content)
      }
    } catch (error) {
      console.error('Error discovering content:', error)
    }
  }

  useEffect(() => {
    // Auto-discover content when tab changes
    handleDiscoverContent()
  }, [activeTab])

  const formatLastUpdated = (date: Date | null) => {
    if (!date) return 'Never'
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    const days = Math.floor(hours / 24)
    return `${days}d ago`
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">Content Discovery</CardTitle>
                <p className="text-sm text-muted-foreground">
                  AI-powered content recommendations tailored to your preferences
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="gap-1">
                <Clock className="h-3 w-3" />
                {formatLastUpdated(lastUpdated)}
              </Badge>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleDiscoverContent}
                disabled={isLoading}
                className="gap-2"
              >
                <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
                Refresh
              </Button>
              
              {showAdvancedControls && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAlgorithmSettings(!showAlgorithmSettings)}
                  className="gap-2"
                >
                  <Settings className="h-4 w-4" />
                  Settings
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Algorithm Settings */}
      <AnimatePresence>
        {showAlgorithmSettings && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Algorithm Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {algorithms.map((algorithm) => (
                  <div key={algorithm.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Switch
                          checked={algorithm.enabled}
                          onCheckedChange={(enabled) => toggleAlgorithm(algorithm.id, enabled)}
                        />
                        <div>
                          <div className="font-medium text-sm">{algorithm.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {algorithm.description}
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(algorithm.weight * 100)}%
                      </Badge>
                    </div>
                    
                    {algorithm.enabled && (
                      <div className="ml-8 space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span>Weight</span>
                          <span>{Math.round(algorithm.weight * 100)}%</span>
                        </div>
                        <Slider
                          value={[algorithm.weight * 100]}
                          onValueChange={([value]) => updateAlgorithmWeight(algorithm.id, value / 100)}
                          max={100}
                          min={0}
                          step={5}
                          className="w-full"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Discovery Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="personalized" className="gap-2">
            <Target className="h-4 w-4" />
            For You
          </TabsTrigger>
          <TabsTrigger value="mood" className="gap-2">
            <Sparkles className="h-4 w-4" />
            Mood
          </TabsTrigger>
          <TabsTrigger value="trending" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            Trending
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personalized" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Personalized Feed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Content curated specifically for you based on your interests, social connections, 
                  and engagement patterns.
                </p>
                
                {/* Metrics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-500">
                      {Math.round(metrics.clickThroughRate * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">Click Rate</div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-green-500">
                      {Math.round(metrics.engagementRate * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">Engagement</div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-500">
                      {Math.round(metrics.diversityScore * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">Diversity</div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-500">
                      {metrics.totalRecommendations}
                    </div>
                    <div className="text-xs text-muted-foreground">Total Items</div>
                  </div>
                </div>

                <Button 
                  onClick={handleDiscoverContent}
                  disabled={isLoading}
                  className="w-full gap-2"
                >
                  <Shuffle className="h-4 w-4" />
                  Generate Personalized Feed
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mood" className="space-y-4">
          <MoodSelector
            selectedMoods={selectedMoods}
            onMoodChange={setSelectedMoods}
            maxSelections={3}
            variant="expanded"
            showIntensity={true}
            showTrending={true}
          />
          
          {selectedMoods.length > 0 && (
            <Card>
              <CardContent className="p-4">
                <Button 
                  onClick={handleDiscoverContent}
                  disabled={isLoading}
                  className="w-full gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  Discover {selectedMoods.join(' + ')} Content
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="trending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Trending Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Discover what's popular right now based on engagement velocity and viral potential.
                </p>
                
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    onClick={() => getTrendingContent('hour')}
                    className="gap-2"
                  >
                    <Zap className="h-4 w-4" />
                    Last Hour
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => getTrendingContent('day')}
                    className="gap-2"
                  >
                    <Clock className="h-4 w-4" />
                    Today
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => getTrendingContent('week')}
                    className="gap-2"
                  >
                    <BarChart3 className="h-4 w-4" />
                    This Week
                  </Button>
                </div>

                <Button 
                  onClick={handleDiscoverContent}
                  disabled={isLoading}
                  className="w-full gap-2"
                >
                  <TrendingUp className="h-4 w-4" />
                  Show Trending Content
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
              <span className="text-sm text-muted-foreground">
                Discovering amazing content for you...
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
