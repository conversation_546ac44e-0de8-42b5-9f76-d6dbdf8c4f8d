"use client"

import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { FollowButton } from '@/components/social/follow-button'
import { UserSuggestions } from '@/components/social/user-suggestions'
import { 
  Verified, 
  MapPin, 
  Calendar, 
  Link as LinkIcon,
  Users,
  FileText,
  Heart,
  MessageCircle,
  Settings,
  Share2
} from 'lucide-react'
import { toast } from 'sonner'

interface UserProfile {
  id: string
  username: string
  displayName: string
  bio?: string
  avatarUrl?: string
  coverImageUrl?: string
  isVerified: boolean
  location?: string
  website?: string
  joinedAt: Date
  followerCount: number
  followingCount: number
  postCount: number
  isOwnProfile: boolean
}

interface UserPost {
  id: string
  title?: string
  content: string
  contentType: string
  contentUrl?: string
  thumbnailUrl?: string
  createdAt: Date
  likeCount: number
  commentCount: number
  shareCount: number
}

export default function ProfilePage() {
  const params = useParams()
  const username = params.username as string
  
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [posts, setPosts] = useState<UserPost[]>([])
  const [loading, setLoading] = useState(true)
  const [postsLoading, setPostsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('posts')

  useEffect(() => {
    loadProfile()
  }, [username])

  useEffect(() => {
    if (profile) {
      loadUserPosts()
    }
  }, [profile, activeTab])

  const loadProfile = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/user/profile/${username}`)
      
      if (!response.ok) {
        throw new Error('Failed to load profile')
      }

      const data = await response.json()
      setProfile(data.profile)
    } catch (error) {
      console.error('Error loading profile:', error)
      toast.error('Failed to load profile')
    } finally {
      setLoading(false)
    }
  }

  const loadUserPosts = async () => {
    if (!profile) return
    
    try {
      setPostsLoading(true)
      const response = await fetch(`/api/user/${profile.id}/posts?tab=${activeTab}`)
      
      if (!response.ok) {
        throw new Error('Failed to load posts')
      }

      const data = await response.json()
      setPosts(data.posts)
    } catch (error) {
      console.error('Error loading posts:', error)
      toast.error('Failed to load posts')
    } finally {
      setPostsLoading(false)
    }
  }

  const formatCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'long',
      year: 'numeric'
    }).format(new Date(date))
  }

  if (loading) {
    return <ProfileSkeleton />
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-12">
            <h1 className="text-2xl font-bold mb-2">User not found</h1>
            <p className="text-muted-foreground">
              The profile you're looking for doesn't exist.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Profile Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Profile Header */}
          <Card>
            <CardContent className="p-6">
              {/* Cover Image */}
              {profile.coverImageUrl && (
                <div className="h-32 md:h-48 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mb-6 relative overflow-hidden">
                  <img 
                    src={profile.coverImageUrl} 
                    alt="Cover" 
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Profile Info */}
              <div className="flex flex-col md:flex-row gap-4 items-start">
                <Avatar className="h-24 w-24 border-4 border-background">
                  <AvatarImage src={profile.avatarUrl} alt={profile.displayName} />
                  <AvatarFallback className="text-2xl">
                    {profile.displayName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-3">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
                    <div>
                      <div className="flex items-center gap-2">
                        <h1 className="text-2xl font-bold">{profile.displayName}</h1>
                        {profile.isVerified && (
                          <Verified className="h-6 w-6 text-blue-500 fill-current" />
                        )}
                      </div>
                      <p className="text-muted-foreground">@{profile.username}</p>
                    </div>

                    <div className="flex gap-2">
                      {!profile.isOwnProfile && (
                        <FollowButton
                          userId={profile.id}
                          username={profile.username}
                          displayName={profile.displayName}
                        />
                      )}
                      {profile.isOwnProfile && (
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-2" />
                          Edit Profile
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Bio */}
                  {profile.bio && (
                    <p className="text-sm">{profile.bio}</p>
                  )}

                  {/* Meta Info */}
                  <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                    {profile.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        <span>{profile.location}</span>
                      </div>
                    )}
                    {profile.website && (
                      <div className="flex items-center gap-1">
                        <LinkIcon className="h-4 w-4" />
                        <a 
                          href={profile.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline"
                        >
                          {profile.website}
                        </a>
                      </div>
                    )}
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>Joined {formatDate(profile.joinedAt)}</span>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex gap-6 text-sm">
                    <div className="flex items-center gap-1">
                      <span className="font-semibold">{formatCount(profile.followingCount)}</span>
                      <span className="text-muted-foreground">Following</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="font-semibold">{formatCount(profile.followerCount)}</span>
                      <span className="text-muted-foreground">Followers</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="font-semibold">{formatCount(profile.postCount)}</span>
                      <span className="text-muted-foreground">Posts</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="posts">Posts</TabsTrigger>
              <TabsTrigger value="media">Media</TabsTrigger>
              <TabsTrigger value="likes">Likes</TabsTrigger>
              <TabsTrigger value="memories">Memories</TabsTrigger>
            </TabsList>

            <TabsContent value="posts" className="space-y-4">
              {postsLoading ? (
                <PostsSkeleton />
              ) : posts.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">No posts yet</h3>
                    <p className="text-muted-foreground">
                      {profile.isOwnProfile 
                        ? "Share your first post to get started!" 
                        : `${profile.displayName} hasn't posted anything yet.`
                      }
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {posts.map((post) => (
                    <PostCard key={post.id} post={post} />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="media">
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-muted-foreground">Media content coming soon...</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="likes">
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-muted-foreground">Liked posts coming soon...</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="memories">
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-muted-foreground">Saved memories coming soon...</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <UserSuggestions 
            title="People you might know"
            limit={5}
            variant="card"
          />
        </div>
      </div>
    </div>
  )
}

function PostCard({ post }: { post: UserPost }) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          {post.title && (
            <h3 className="font-semibold">{post.title}</h3>
          )}
          <p className="text-sm">{post.content}</p>
          
          {post.thumbnailUrl && (
            <div className="rounded-lg overflow-hidden">
              <img 
                src={post.thumbnailUrl} 
                alt="Post media" 
                className="w-full h-48 object-cover"
              />
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Heart className="h-4 w-4" />
                {post.likeCount}
              </span>
              <span className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                {post.commentCount}
              </span>
              <span className="flex items-center gap-1">
                <Share2 className="h-4 w-4" />
                {post.shareCount}
              </span>
            </div>
            <span>{new Date(post.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function ProfileSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-6">
              <Skeleton className="h-48 w-full mb-6" />
              <div className="flex gap-4">
                <Skeleton className="h-24 w-24 rounded-full" />
                <div className="flex-1 space-y-3">
                  <Skeleton className="h-8 w-48" />
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-full" />
                  <div className="flex gap-4">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-6">
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    </div>
  )
}

function PostsSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-4">
            <div className="space-y-3">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-48 w-full" />
              <div className="flex justify-between">
                <div className="flex gap-4">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                </div>
                <Skeleton className="h-4 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
