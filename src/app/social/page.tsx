"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { UserSuggestions } from '@/components/social/user-suggestions'
import { Notifications } from '@/components/social/notifications'
import { SocialInteractions } from '@/components/social/social-interactions'
import { 
  Home, 
  Compass, 
  TrendingUp, 
  Heart, 
  Users, 
  Bell,
  Plus,
  Search,
  Settings,
  Sparkles
} from 'lucide-react'
import { FeedType } from '@/types/feed'
import { useSocialStore, selectUnreadCount } from '@/lib/stores/social-store'
import Link from 'next/link'

export default function SocialPage() {
  const [activeFeed, setActiveFeed] = useState<FeedType>(FeedType.DISCOVER)
  const [showNotifications, setShowNotifications] = useState(false)
  const unreadCount = useSocialStore(selectUnreadCount)

  const feedTabs = [
    {
      id: FeedType.DISCOVER,
      label: 'Discover',
      icon: Compass,
      description: 'Explore new content and creators',
    },
    {
      id: FeedType.FOLLOWING,
      label: 'Following',
      icon: Users,
      description: 'Content from people you follow',
    },
    {
      id: FeedType.TRENDING,
      label: 'Trending',
      icon: TrendingUp,
      description: 'What\'s popular right now',
    },
    {
      id: FeedType.MOOD,
      label: 'Mood Match',
      icon: Sparkles,
      description: 'Content that matches your vibe',
    },
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <Heart className="h-4 w-4 text-white" />
              </div>
              <span className="font-bold text-xl">HVPPY</span>
            </Link>

            {/* Search */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search creators, content, moods..."
                  className="w-full pl-10 pr-4 py-2 border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/studio">
                  <Plus className="h-4 w-4 mr-2" />
                  Create
                </Link>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="relative"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <Bell className="h-4 w-4" />
                {unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs"
                  >
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </Button>

              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Feed */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">Your Feed</CardTitle>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Sparkles className="h-4 w-4 mr-2" />
                      Mood Filter
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <Tabs value={activeFeed} onValueChange={(value) => setActiveFeed(value as FeedType)}>
                  <div className="px-6 pb-4">
                    <TabsList className="grid w-full grid-cols-4">
                      {feedTabs.map((tab) => {
                        const Icon = tab.icon
                        return (
                          <TabsTrigger key={tab.id} value={tab.id} className="gap-2">
                            <Icon className="h-4 w-4" />
                            <span className="hidden sm:inline">{tab.label}</span>
                          </TabsTrigger>
                        )
                      })}
                    </TabsList>
                  </div>

                  {feedTabs.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id} className="mt-0">
                      <div className="px-6 pb-4">
                        <p className="text-sm text-muted-foreground mb-4">
                          {tab.description}
                        </p>
                      </div>
                      
                      <div className="h-[calc(100vh-300px)] overflow-hidden">
                        <VerticalFeedContainer
                          feedType={tab.id}
                          className="h-full w-full"
                          showControls={true}
                          enableSwipeGestures={true}
                          preloadDistance={3}
                          offloadDistance={10}
                        />
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Notifications */}
            {showNotifications && (
              <Notifications
                variant="sidebar"
                className="lg:sticky lg:top-24"
                maxHeight="400px"
              />
            )}

            {/* User Suggestions */}
            <UserSuggestions
              title="Discover Creators"
              limit={5}
              variant="card"
              className="lg:sticky lg:top-24"
            />

            {/* Trending Topics */}
            <Card className="lg:sticky lg:top-24">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Trending Moods
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { mood: 'Energetic', count: '2.1K posts', color: 'bg-orange-500' },
                  { mood: 'Chill', count: '1.8K posts', color: 'bg-blue-500' },
                  { mood: 'Creative', count: '1.5K posts', color: 'bg-purple-500' },
                  { mood: 'Happy', count: '1.2K posts', color: 'bg-yellow-500' },
                  { mood: 'Nostalgic', count: '980 posts', color: 'bg-pink-500' },
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={cn('h-3 w-3 rounded-full', item.color)} />
                      <span className="font-medium">{item.mood}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">{item.count}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="lg:sticky lg:top-24">
              <CardHeader>
                <CardTitle className="text-lg">Your Activity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-500">127</div>
                    <div className="text-sm text-muted-foreground">Posts</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-500">2.3K</div>
                    <div className="text-sm text-muted-foreground">Followers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-500">456</div>
                    <div className="text-sm text-muted-foreground">Following</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-pink-500">89</div>
                    <div className="text-sm text-muted-foreground">Memories</div>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/profile">View Profile</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
