"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { ModernVerticalFeedContainer } from '@/components/feed/modern-vertical-feed-container'
import { FeedType } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Zap, 
  TrendingUp, 
  Users, 
  Heart, 
  Settings, 
  Monitor,
  Smartphone,
  Tablet,
  RotateCcw,
  Play,
  Pause,
  Volume2,
  VolumeX
} from 'lucide-react'

export default function ModernFeedTestPage() {
  const [selectedFeed, setSelectedFeed] = useState<FeedType>(FeedType.DISCOVER)
  const [enableVirtualScrolling, setEnableVirtualScrolling] = useState(true)
  const [enablePerformanceMonitoring, setEnablePerformanceMonitoring] = useState(true)
  const [enableRealTimeUpdates, setEnableRealTimeUpdates] = useState(true)
  const [maxConcurrentPlayers, setMaxConcurrentPlayers] = useState(1)
  const [devicePreview, setDevicePreview] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  const feedTypes = [
    { id: FeedType.DISCOVER, label: 'Discover', icon: Zap, color: 'purple' },
    { id: FeedType.TRENDING, label: 'Trending', icon: TrendingUp, color: 'orange' },
    { id: FeedType.FOLLOWING, label: 'Following', icon: Users, color: 'blue' },
    { id: FeedType.LIKED, label: 'Liked', icon: Heart, color: 'pink' },
  ]

  const deviceSizes = {
    desktop: { width: '100%', height: '100vh' },
    tablet: { width: '768px', height: '1024px' },
    mobile: { width: '375px', height: '812px' },
  }

  const handleInteraction = (type: string, data: any) => {
    console.log('Feed Interaction:', { type, data })
  }

  const handleItemChange = (item: any, index: number) => {
    console.log('Item Changed:', { item: item?.post?.title, index })
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="border-b border-white/10 bg-black/50 backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Modern Feed Test
              </h1>
              <p className="text-white/60 text-sm">
                TikTok-inspired vertical feed with enhanced features
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Device Preview Toggle */}
              <div className="flex items-center space-x-1 bg-white/10 rounded-lg p-1">
                {[
                  { id: 'desktop', icon: Monitor },
                  { id: 'tablet', icon: Tablet },
                  { id: 'mobile', icon: Smartphone },
                ].map(({ id, icon: Icon }) => (
                  <Button
                    key={id}
                    variant={devicePreview === id ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setDevicePreview(id as any)}
                    className="h-8 w-8 p-0"
                  >
                    <Icon className="w-4 h-4" />
                  </Button>
                ))}
              </div>

              {/* Settings */}
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar Controls */}
        <div className="w-80 border-r border-white/10 bg-black/30 p-6 overflow-y-auto">
          <div className="space-y-6">
            {/* Feed Type Selection */}
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle className="text-white text-lg">Feed Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {feedTypes.map(({ id, label, icon: Icon, color }) => (
                    <Button
                      key={id}
                      variant={selectedFeed === id ? 'default' : 'outline'}
                      onClick={() => setSelectedFeed(id)}
                      className={`h-auto p-3 flex flex-col items-center space-y-2 ${
                        selectedFeed === id 
                          ? `bg-${color}-600 hover:bg-${color}-700` 
                          : 'bg-white/5 border-white/20 hover:bg-white/10'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="text-xs">{label}</span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Settings */}
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle className="text-white text-lg">Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/80">Virtual Scrolling</span>
                  <Button
                    variant={enableVirtualScrolling ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEnableVirtualScrolling(!enableVirtualScrolling)}
                  >
                    {enableVirtualScrolling ? 'ON' : 'OFF'}
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/80">Performance Monitor</span>
                  <Button
                    variant={enablePerformanceMonitoring ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEnablePerformanceMonitoring(!enablePerformanceMonitoring)}
                  >
                    {enablePerformanceMonitoring ? 'ON' : 'OFF'}
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/80">Real-time Updates</span>
                  <Button
                    variant={enableRealTimeUpdates ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEnableRealTimeUpdates(!enableRealTimeUpdates)}
                  >
                    {enableRealTimeUpdates ? 'ON' : 'OFF'}
                  </Button>
                </div>

                <div className="space-y-2">
                  <span className="text-sm text-white/80">Max Concurrent Players</span>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3].map((num) => (
                      <Button
                        key={num}
                        variant={maxConcurrentPlayers === num ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setMaxConcurrentPlayers(num)}
                        className="w-8 h-8 p-0"
                      >
                        {num}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Feature Status */}
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle className="text-white text-lg">Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {[
                    { label: 'Snap Scrolling', status: 'active' },
                    { label: 'Auto-play Media', status: 'active' },
                    { label: 'Infinite Scroll', status: 'active' },
                    { label: 'Content Preloading', status: 'active' },
                    { label: 'Optimistic Updates', status: 'active' },
                    { label: 'Touch Gestures', status: 'active' },
                  ].map(({ label, status }) => (
                    <div key={label} className="flex items-center justify-between">
                      <span className="text-sm text-white/80">{label}</span>
                      <Badge 
                        variant="outline" 
                        className="bg-green-500/20 text-green-400 border-green-500/30"
                      >
                        ✓ {status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle className="text-white text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Refresh Feed
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Play className="w-4 h-4 mr-2" />
                  Play All
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Pause className="w-4 h-4 mr-2" />
                  Pause All
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <VolumeX className="w-4 h-4 mr-2" />
                  Mute All
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Feed Preview */}
        <div className="flex-1 flex items-center justify-center bg-gray-900/50">
          <motion.div
            key={devicePreview}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="relative bg-black rounded-lg overflow-hidden shadow-2xl"
            style={deviceSizes[devicePreview]}
          >
            {/* Device Frame */}
            {devicePreview !== 'desktop' && (
              <div className="absolute inset-0 pointer-events-none z-50">
                <div className="w-full h-full border-4 border-gray-800 rounded-lg" />
                {devicePreview === 'mobile' && (
                  <>
                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gray-600 rounded-full" />
                    <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gray-800 rounded-full" />
                  </>
                )}
              </div>
            )}

            {/* Modern Feed */}
            <ModernVerticalFeedContainer
              feedType={selectedFeed}
              enableVirtualScrolling={enableVirtualScrolling}
              enablePerformanceMonitoring={enablePerformanceMonitoring}
              enableRealTimeUpdates={enableRealTimeUpdates}
              maxConcurrentPlayers={maxConcurrentPlayers}
              onInteraction={handleInteraction}
              onItemChange={handleItemChange}
              className="w-full h-full"
            />
          </motion.div>
        </div>
      </div>
    </div>
  )
}
