@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar: 216 7.9365% 87.6471%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --font-sans: var(--font-inter), system-ui, sans-serif;
    --font-serif: var(--font-cal-sans), system-ui, serif;
    --font-mono: var(--font-jetbrains-mono), monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
    --spacing: 0.25rem;
    --sidebar-background: 0 0% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar: 222.8571 20% 20.5882%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --font-sans: var(--font-inter), system-ui, sans-serif;
    --font-serif: var(--font-cal-sans), system-ui, serif;
    --font-mono: var(--font-jetbrains-mono), monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0px 1px 3px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
    --sidebar-background: 240 5.9% 10%;
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom HVPPY Central styles */
@layer components {
  .hvppy-gradient {
    @apply bg-gradient-to-r from-hvppy-500 to-hvppy-700;
  }

  .mood-happy {
    @apply bg-mood-happy text-black;
  }

  .mood-chill {
    @apply bg-mood-chill text-black;
  }

  .mood-heartbroken {
    @apply bg-mood-heartbroken text-white;
  }

  .mood-inspired {
    @apply bg-mood-inspired text-white;
  }

  .mood-energetic {
    @apply bg-mood-energetic text-white;
  }

  .mood-peaceful {
    @apply bg-mood-peaceful text-black;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .content-card {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced feed styles */
  .feed-container {
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .feed-item {
    scroll-snap-align: start;
    scroll-snap-stop: always;
  }

  /* Performance optimizations */
  .feed-container * {
    will-change: transform;
  }

  /* Smooth scrolling for better UX */
  @media (prefers-reduced-motion: no-preference) {
    .feed-container {
      scroll-behavior: smooth;
    }
  }

  .creator-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-hvppy-100 text-hvppy-800 dark:bg-hvppy-900 dark:text-hvppy-200;
  }

  /* Full-screen content styles */
  .full-screen-content {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  /* Ensure no body margins/padding for full-screen pages */
  .full-screen-page {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
  }
}
