"use client"

import React, { useState, useEffect } from 'react'
import { ModernVerticalFeedContainer } from '@/components/feed/modern-vertical-feed-container'
import { MoodSelector } from '@/components/discovery/mood-selector'
import { Button } from '@/components/ui/button'
import {
  Play,
  Pause
} from 'lucide-react'
import { FeedType, FeedItem, InteractionData } from '@/types/feed'
import { ContentType } from '@/types'
import { useContentDiscovery } from '@/hooks/discovery/use-content-discovery'

export default function EnhancedSocialPage() {
  const [activeTab, setActiveTab] = useState('discover')
  const [selectedMoods, setSelectedMoods] = useState<string[]>(['happy', 'energetic'])
  const [discoveredContent, setDiscoveredContent] = useState<FeedItem[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [showPerformance, setShowPerformance] = useState(false)

  const { discoverContent, isLoading } = useContentDiscovery()

  // Mock content for demonstration
  const mockContent: FeedItem[] = [
    {
      id: 'demo-1',
      post: {
        id: 'demo-1',
        userId: 'user-1',
        title: 'Amazing Sunset Vibes',
        content: 'Caught this incredible sunset today! The colors were absolutely magical. Sometimes nature just takes your breath away. 🌅✨',
        contentType: ContentType.IMAGE,
        contentUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=1200&fit=crop',
        mediaUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=1200&fit=crop'],
        thumbnailUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop',
        transcodingStatus: 'COMPLETED' as const,
        moderationStatus: 'APPROVED' as const,
        moods: ['happy', 'peaceful', 'nostalgic'],
        viewCount: 2456,
        likeCount: 1247,
        shareCount: 156,
        reactions: [],
        memories: [],
        comments: [],
        isPublic: true,
        isExperimental: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
        publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        user: {
          id: 'user-1',
          appwriteId: 'appwrite-user-1',
          email: '<EMAIL>',
          username: 'naturelover',
          displayName: 'Sarah Chen',
          avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          role: 'user',
          isVerified: true,
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        },
      },
      metadata: {
        algorithm: 'mood_based',
        score: 0.95,
      },
    },
    {
      id: 'demo-2',
      post: {
        id: 'demo-2',
        userId: 'user-2',
        title: 'Creative Flow State',
        content: 'When inspiration strikes and everything just flows... This is what pure creativity feels like! 🎨💫',
        contentType: ContentType.TEXT,
        mediaUrls: [],
        transcodingStatus: 'COMPLETED' as const,
        moderationStatus: 'APPROVED' as const,
        moods: ['creative', 'energetic', 'inspired'],
        viewCount: 1456,
        likeCount: 892,
        shareCount: 234,
        reactions: [],
        memories: [],
        comments: [],
        isPublic: true,
        isExperimental: false,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
        publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        user: {
          id: 'user-2',
          appwriteId: 'appwrite-user-2',
          email: '<EMAIL>',
          username: 'artflow',
          displayName: 'Alex Rivera',
          avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
          role: 'user',
          isVerified: false,
          createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        },
      },
      metadata: {
        algorithm: 'engagement_based',
        score: 0.88,
      },
    },
    {
      id: 'demo-3',
      post: {
        id: 'demo-3',
        userId: 'user-3',
        title: 'Midnight Thoughts',
        content: 'Sometimes the best ideas come in the quiet moments between sleep and dreams. What keeps you up at night? 🌙💭',
        contentType: ContentType.TEXT,
        mediaUrls: [],
        transcodingStatus: 'COMPLETED' as const,
        moderationStatus: 'APPROVED' as const,
        moods: ['mysterious', 'contemplative', 'peaceful'],
        viewCount: 890,
        likeCount: 567,
        shareCount: 89,
        reactions: [],
        memories: [],
        comments: [],
        isPublic: true,
        isExperimental: false,
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
        publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        user: {
          id: 'user-3',
          appwriteId: 'appwrite-user-3',
          email: '<EMAIL>',
          username: 'nightthinker',
          displayName: 'Maya Patel',
          avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
          role: 'user',
          isVerified: true,
          createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        },
      },
      metadata: {
        algorithm: 'similarity_based',
        score: 0.76,
      },
    },
  ]

  useEffect(() => {
    setDiscoveredContent(mockContent)
  }, [])

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % discoveredContent.length)
  }

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + discoveredContent.length) % discoveredContent.length)
  }

  return (
    <div className="full-screen-content bg-black">
      {/* Full Screen Content Feed */}
      <div className="relative w-full h-full">
        <ModernVerticalFeedContainer
          feedType={FeedType.DISCOVER}
          enableVirtualScrolling={true}
          enablePerformanceMonitoring={true}
          enableRealTimeUpdates={true}
          maxConcurrentPlayers={1}
          filters={{ moods: selectedMoods }}
          onInteraction={(interaction: InteractionData) => {
            console.log('Enhanced Feed Interaction:', interaction)
          }}
          onItemChange={(item, index) => {
            setCurrentIndex(index)
            console.log('Item changed:', item?.post?.title, index)
          }}
          className="w-full h-full"
        />

        {/* Navigation Controls - Overlay */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center gap-3 bg-black/30 backdrop-blur-md rounded-full px-4 py-2 z-50">
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePrevious}
            className="text-white hover:bg-white/20 rounded-full p-2"
          >
            ←
          </Button>

          <div className="flex items-center gap-1">
            {discoveredContent.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-white' : 'bg-white/40'
                }`}
              />
            ))}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleNext}
            className="text-white hover:bg-white/20 rounded-full p-2"
          >
            →
          </Button>
        </div>

        {/* Playback Controls - Overlay */}
        <div className="absolute top-6 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black/30 backdrop-blur-md rounded-full px-3 py-2 z-50">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className="text-white hover:bg-white/20 rounded-full p-2"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>

          <div className="text-white text-sm font-medium">
            {currentIndex + 1} / {discoveredContent.length}
          </div>
        </div>

        {/* Mood Selector - Overlay */}
        <div className="absolute top-6 right-6 z-50">
          <MoodSelector
            selectedMoods={selectedMoods}
            onMoodChange={setSelectedMoods}
            className="bg-black/30 backdrop-blur-md rounded-lg"
          />
        </div>
      </div>
    </div>
  )
}
