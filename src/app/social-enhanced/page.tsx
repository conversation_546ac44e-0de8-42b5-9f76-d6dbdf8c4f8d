"use client"

import React, { useState, useEffect } from 'react'
import { SocialLayout } from '@/components/layout/social-layout'
import { ModernVerticalFeedContainer } from '@/components/feed/modern-vertical-feed-container'
import { MoodSelector } from '@/components/discovery/mood-selector'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Play,
  Pause,
  Loader2
} from 'lucide-react'
import { FeedType, FeedItem, InteractionData } from '@/types/feed'
import { ContentType } from '@/types'
import { loadHVPPYContent, filterContentByMood } from '@/lib/content/hvppy-content-loader'

export default function EnhancedSocialPage() {
  const [selectedMoods, setSelectedMoods] = useState<string[]>(['energetic', 'confident'])
  const [discoveredContent, setDiscoveredContent] = useState<FeedItem[]>([])
  const [allContent, setAllContent] = useState<FeedItem[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [isLoading, setIsLoading] = useState(true)

  // Load HVPPY content on component mount
  useEffect(() => {
    const loadContent = async () => {
      setIsLoading(true)
      try {
        const content = await loadHVPPYContent()
        setAllContent(content)

        // Apply initial mood filter
        const filteredContent = filterContentByMood(content, selectedMoods)
        setDiscoveredContent(filteredContent.length > 0 ? filteredContent : content)
      } catch (error) {
        console.error('Error loading HVPPY content:', error)
        // Fallback to empty array if loading fails
        setDiscoveredContent([])
      } finally {
        setIsLoading(false)
      }
    }

    loadContent()
  }, [])

  // Update content when moods change
  useEffect(() => {
    if (allContent.length > 0) {
      const filteredContent = filterContentByMood(allContent, selectedMoods)
      setDiscoveredContent(filteredContent.length > 0 ? filteredContent : allContent)
      setCurrentIndex(0) // Reset to first item when filter changes
    }
  }, [selectedMoods, allContent])

  // Navigation handlers

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % discoveredContent.length)
  }

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + discoveredContent.length) % discoveredContent.length)
  }

  // Show loading state while content is being loaded
  if (isLoading) {
    return (
      <SocialLayout showSidebar={true} showHeader={false}>
        <div className="flex h-screen">
          <div className="flex-1 relative bg-black flex items-center justify-center">
            <div className="text-center text-white">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-lg font-medium">Loading HVPPY Content...</p>
              <p className="text-sm text-white/70 mt-2">Preparing your personalized feed</p>
            </div>
          </div>
        </div>
      </SocialLayout>
    )
  }

  // Show empty state if no content is available
  if (!isLoading && discoveredContent.length === 0) {
    return (
      <SocialLayout showSidebar={true} showHeader={false}>
        <div className="flex h-screen">
          <div className="flex-1 relative bg-black flex items-center justify-center">
            <div className="text-center text-white">
              <p className="text-lg font-medium">No content available</p>
              <p className="text-sm text-white/70 mt-2">Try adjusting your mood filters</p>
            </div>
          </div>
        </div>
      </SocialLayout>
    )
  }

  return (
    <SocialLayout showSidebar={true} showHeader={false}>
      <div className="flex h-screen">
        {/* Main Content Area - Full Width/Height with No Padding */}
        <div className="flex-1 relative bg-black">
          <ModernVerticalFeedContainer
            feedType={FeedType.DISCOVER}
            enableVirtualScrolling={true}
            enablePerformanceMonitoring={true}
            enableRealTimeUpdates={true}
            maxConcurrentPlayers={1}
            filters={{ moods: selectedMoods }}
            onInteraction={(interaction: InteractionData) => {
              console.log('Enhanced Feed Interaction:', interaction)
            }}
            onItemChange={(item, index) => {
              setCurrentIndex(index)
              console.log('Item changed:', item?.post?.title, index)
            }}
            className="w-full h-full"
          />

          {/* Navigation Controls - Overlay */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center gap-3 bg-black/30 backdrop-blur-md rounded-full px-4 py-2 z-50">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevious}
              className="text-white hover:bg-white/20 rounded-full p-2"
            >
              ←
            </Button>

            <div className="flex items-center gap-1">
              {discoveredContent.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-white' : 'bg-white/40'
                  }`}
                />
              ))}
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleNext}
              className="text-white hover:bg-white/20 rounded-full p-2"
            >
              →
            </Button>
          </div>

          {/* Playback Controls - Overlay */}
          <div className="absolute top-6 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black/30 backdrop-blur-md rounded-full px-3 py-2 z-50">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsPlaying(!isPlaying)}
              className="text-white hover:bg-white/20 rounded-full p-2"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>

            <div className="text-white text-sm font-medium">
              {currentIndex + 1} / {discoveredContent.length}
            </div>
          </div>

          {/* Mood Selector - Overlay */}
          <div className="absolute top-6 right-6 z-50">
            <MoodSelector
              selectedMoods={selectedMoods}
              onMoodChange={setSelectedMoods}
              variant="compact"
              maxSelections={5}
              className="bg-black/30 backdrop-blur-md rounded-lg"
            />
          </div>

          {/* Content Info - Overlay */}
          <div className="absolute bottom-20 left-6 z-50 text-white">
            <div className="bg-black/30 backdrop-blur-md rounded-lg px-3 py-2">
              <p className="text-sm font-medium">
                {discoveredContent.length} HVPPY tracks
              </p>
              <p className="text-xs text-white/70">
                {selectedMoods.length > 0 ? `Filtered by: ${selectedMoods.join(', ')}` : 'All content'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </SocialLayout>
  )
}
