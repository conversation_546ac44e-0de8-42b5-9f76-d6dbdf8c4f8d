"use client"

import React, { useState } from 'react'
import { SocialLayout } from '@/components/layout/social-layout'
import { ContentCreator } from '@/components/social/content-creator'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sparkles, 
  Camera, 
  Music, 
  Video, 
  FileText,
  Palette,
  Wand2,
  Upload,
  Save,
  Eye,
  Share2,
  BarChart3,
  Clock,
  TrendingUp,
  Heart,
  Play
} from 'lucide-react'
import { ContentType } from '@/types'
import { motion } from 'framer-motion'
import { toast } from 'sonner'

export default function EnhancedStudioPage() {
  const [activeTab, setActiveTab] = useState('create')
  const [selectedContentType, setSelectedContentType] = useState<ContentType>(ContentType.TEXT)

  const contentTypes = [
    {
      type: ContentType.TEXT,
      label: 'Text Post',
      icon: FileText,
      description: 'Share your thoughts and stories',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      type: ContentType.IMAGE,
      label: 'Image',
      icon: Camera,
      description: 'Upload and edit photos',
      gradient: 'from-green-500 to-teal-500'
    },
    {
      type: ContentType.VIDEO,
      label: 'Video',
      icon: Video,
      description: 'Create and edit videos',
      gradient: 'from-red-500 to-pink-500'
    },
    {
      type: ContentType.MUSIC,
      label: 'Audio',
      icon: Music,
      description: 'Record and mix audio',
      gradient: 'from-purple-500 to-indigo-500'
    },
  ]

  const aiTools = [
    {
      id: 'text-generator',
      name: 'AI Text Generator',
      description: 'Generate engaging captions and stories',
      icon: Wand2,
      color: 'text-blue-500'
    },
    {
      id: 'image-enhancer',
      name: 'Image Enhancer',
      description: 'Enhance and stylize your photos',
      icon: Palette,
      color: 'text-green-500'
    },
    {
      id: 'video-editor',
      name: 'Smart Video Editor',
      description: 'AI-powered video editing and effects',
      icon: Video,
      color: 'text-red-500'
    },
    {
      id: 'music-composer',
      name: 'Music Composer',
      description: 'Create beats and melodies with AI',
      icon: Music,
      color: 'text-purple-500'
    },
  ]

  const recentProjects = [
    {
      id: '1',
      title: 'Summer Vibes Mix',
      type: 'audio',
      thumbnail: '/placeholder-audio.jpg',
      lastEdited: '2 hours ago',
      status: 'draft'
    },
    {
      id: '2',
      title: 'City Photography Series',
      type: 'image',
      thumbnail: '/placeholder-image.jpg',
      lastEdited: '1 day ago',
      status: 'published'
    },
    {
      id: '3',
      title: 'Creative Process Vlog',
      type: 'video',
      thumbnail: '/placeholder-video.jpg',
      lastEdited: '3 days ago',
      status: 'draft'
    },
  ]

  const handleContentPublish = (content: any) => {
    toast.success('Content published successfully!')
    console.log('Published content:', content)
  }

  return (
    <SocialLayout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
                    <Sparkles className="h-6 w-6 text-white" />
                  </div>
                  AI Content Studio
                </h1>
                <p className="text-muted-foreground mt-2">
                  Create amazing content with AI-powered tools and templates
                </p>
              </div>
              
              <div className="flex items-center gap-3">
                <Button variant="outline" className="gap-2">
                  <Save className="h-4 w-4" />
                  Save Draft
                </Button>
                <Button variant="outline" className="gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </Button>
                <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 gap-2">
                  <Share2 className="h-4 w-4" />
                  Publish
                </Button>
              </div>
            </div>

            {/* Studio Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="create" className="gap-2">
                  <Sparkles className="h-4 w-4" />
                  Create
                </TabsTrigger>
                <TabsTrigger value="projects" className="gap-2">
                  <Save className="h-4 w-4" />
                  Projects
                </TabsTrigger>
                <TabsTrigger value="ai-tools" className="gap-2">
                  <Wand2 className="h-4 w-4" />
                  AI Tools
                </TabsTrigger>
                <TabsTrigger value="analytics" className="gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Analytics
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </motion.div>

          <Tabs value={activeTab} className="w-full">
            {/* Create Tab */}
            <TabsContent value="create" className="mt-0">
              <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                {/* Content Type Selection */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className="xl:col-span-1"
                >
                  <Card>
                    <CardHeader>
                      <CardTitle>Content Type</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {contentTypes.map((type) => {
                        const Icon = type.icon
                        const isSelected = selectedContentType === type.type
                        
                        return (
                          <motion.div
                            key={type.type}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div
                              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                                isSelected 
                                  ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' 
                                  : 'hover:border-purple-300 hover:bg-purple-50/50'
                              }`}
                              onClick={() => setSelectedContentType(type.type)}
                            >
                              <div className="flex items-center gap-3">
                                <div className={`p-2 rounded-lg bg-gradient-to-r ${type.gradient}`}>
                                  <Icon className="h-4 w-4 text-white" />
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-medium">{type.label}</h3>
                                  <p className="text-xs text-muted-foreground">{type.description}</p>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        )
                      })}
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Main Creator */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="xl:col-span-3"
                >
                  <ContentCreator
                    defaultContentType={selectedContentType}
                    onPublish={handleContentPublish}
                  />
                </motion.div>
              </div>
            </TabsContent>

            {/* Projects Tab */}
            <TabsContent value="projects" className="mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Projects</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {recentProjects.map((project, index) => (
                        <motion.div
                          key={project.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 + index * 0.1 }}
                        >
                          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                            <div className="aspect-video bg-gradient-to-br from-purple-400 to-pink-400 rounded-t-lg flex items-center justify-center">
                              {project.type === 'audio' && <Music className="h-12 w-12 text-white" />}
                              {project.type === 'image' && <Camera className="h-12 w-12 text-white" />}
                              {project.type === 'video' && <Video className="h-12 w-12 text-white" />}
                            </div>
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between mb-2">
                                <h3 className="font-medium truncate">{project.title}</h3>
                                <Badge variant={project.status === 'published' ? 'default' : 'secondary'}>
                                  {project.status}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                <span>{project.lastEdited}</span>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* AI Tools Tab */}
            <TabsContent value="ai-tools" className="mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>AI-Powered Creation Tools</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {aiTools.map((tool, index) => (
                        <motion.div
                          key={tool.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 + index * 0.1 }}
                        >
                          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                            <CardContent className="p-6">
                              <div className="flex items-start gap-4">
                                <div className="p-3 bg-muted rounded-lg">
                                  <tool.icon className={`h-6 w-6 ${tool.color}`} />
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-semibold mb-2">{tool.name}</h3>
                                  <p className="text-sm text-muted-foreground mb-4">{tool.description}</p>
                                  <Button size="sm" className="gap-2">
                                    <Wand2 className="h-3 w-3" />
                                    Try Now
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Views</p>
                        <p className="text-2xl font-bold">125.4K</p>
                      </div>
                      <Eye className="h-8 w-8 text-blue-500" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span className="text-xs text-green-600">+12.5%</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Likes</p>
                        <p className="text-2xl font-bold">8.9K</p>
                      </div>
                      <Heart className="h-8 w-8 text-red-500" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span className="text-xs text-green-600">+8.2%</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Shares</p>
                        <p className="text-2xl font-bold">2.1K</p>
                      </div>
                      <Share2 className="h-8 w-8 text-green-500" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span className="text-xs text-green-600">+15.3%</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Engagement</p>
                        <p className="text-2xl font-bold">7.8%</p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-purple-500" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span className="text-xs text-green-600">+3.1%</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </SocialLayout>
  )
}
