"use client"

import React, { useState } from 'react'
import { SocialLayout } from '@/components/layout/social-layout'
import { DiscoveryDashboard } from '@/components/discovery/discovery-dashboard'
import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Compass, 
  TrendingUp, 
  Heart, 
  Sparkles,
  Filter,
  Search,
  Grid3X3,
  List,
  Play
} from 'lucide-react'
import { FeedType, FeedItem } from '@/types/feed'
import { motion } from 'framer-motion'

export default function DiscoverPage() {
  const [activeTab, setActiveTab] = useState('all')
  const [viewMode, setViewMode] = useState<'feed' | 'grid' | 'list'>('feed')
  const [discoveredContent, setDiscoveredContent] = useState<FeedItem[]>([])

  const categories = [
    { id: 'all', label: 'All Content', icon: Compass, count: '2.1M' },
    { id: 'trending', label: 'Trending', icon: TrendingUp, count: '156K' },
    { id: 'mood', label: 'Mood Match', icon: Heart, count: '892K' },
    { id: 'new', label: 'Fresh', icon: Sparkles, count: '45K' },
  ]

  const handleContentSelect = (content: FeedItem[]) => {
    setDiscoveredContent(content)
  }

  return (
    <SocialLayout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
                    <Compass className="h-6 w-6 text-white" />
                  </div>
                  Discover
                </h1>
                <p className="text-muted-foreground mt-2">
                  Explore amazing content from creators around the world
                </p>
              </div>
              
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
                
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant={viewMode === 'feed' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('feed')}
                    className="rounded-r-none"
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Category Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-none lg:flex">
                {categories.map((category) => {
                  const Icon = category.icon
                  return (
                    <TabsTrigger
                      key={category.id}
                      value={category.id}
                      className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white"
                    >
                      <Icon className="h-4 w-4" />
                      <span className="hidden sm:inline">{category.label}</span>
                      <Badge variant="secondary" className="ml-1 text-xs">
                        {category.count}
                      </Badge>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </Tabs>
          </motion.div>

          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Discovery Panel */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="xl:col-span-1"
            >
              <DiscoveryDashboard
                onContentSelect={handleContentSelect}
                showAdvancedControls={true}
              />
            </motion.div>

            {/* Main Content Area */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="xl:col-span-3"
            >
              <Tabs value={activeTab} className="w-full">
                <TabsContent value="all" className="mt-0">
                  {viewMode === 'feed' ? (
                    <div className="h-[800px] rounded-lg overflow-hidden">
                      <VerticalFeedContainer
                        feedType={FeedType.DISCOVER}
                        showControls={true}
                        enableSwipeGestures={true}
                        preloadDistance={3}
                        offloadDistance={10}
                      />
                    </div>
                  ) : viewMode === 'grid' ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {Array.from({ length: 12 }, (_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: i * 0.05 }}
                        >
                          <Card className="aspect-square overflow-hidden group cursor-pointer hover:shadow-lg transition-shadow">
                            <div className="relative w-full h-full bg-gradient-to-br from-purple-400 to-pink-400">
                              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                <Play className="h-8 w-8 text-white" />
                              </div>
                              <div className="absolute bottom-2 left-2 right-2">
                                <p className="text-white text-sm font-medium truncate">
                                  Sample Content {i + 1}
                                </p>
                              </div>
                            </div>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {Array.from({ length: 8 }, (_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: i * 0.05 }}
                        >
                          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                            <div className="flex gap-4">
                              <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex-shrink-0" />
                              <div className="flex-1">
                                <h3 className="font-semibold mb-1">Sample Content {i + 1}</h3>
                                <p className="text-sm text-muted-foreground mb-2">
                                  This is a sample description for the content item that would appear in the list view.
                                </p>
                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                  <span className="flex items-center gap-1">
                                    <Heart className="h-3 w-3" />
                                    {Math.floor(Math.random() * 1000)}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Play className="h-3 w-3" />
                                    {Math.floor(Math.random() * 10000)}
                                  </span>
                                  <span>2 hours ago</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="trending" className="mt-0">
                  <Card className="p-8 text-center">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 text-orange-500" />
                    <h3 className="text-xl font-semibold mb-2">Trending Content</h3>
                    <p className="text-muted-foreground">
                      Discover what's hot right now based on engagement and viral potential.
                    </p>
                  </Card>
                </TabsContent>

                <TabsContent value="mood" className="mt-0">
                  <Card className="p-8 text-center">
                    <Heart className="h-12 w-12 mx-auto mb-4 text-pink-500" />
                    <h3 className="text-xl font-semibold mb-2">Mood-Based Discovery</h3>
                    <p className="text-muted-foreground">
                      Content curated to match your current mood and preferences.
                    </p>
                  </Card>
                </TabsContent>

                <TabsContent value="new" className="mt-0">
                  <Card className="p-8 text-center">
                    <Sparkles className="h-12 w-12 mx-auto mb-4 text-purple-500" />
                    <h3 className="text-xl font-semibold mb-2">Fresh Content</h3>
                    <p className="text-muted-foreground">
                      The latest uploads from creators you might love.
                    </p>
                  </Card>
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        </div>
      </div>
    </SocialLayout>
  )
}
