"use client"

import React from 'react'
import { SocialOnboarding } from '@/components/onboarding/social-onboarding'
import { toast } from 'sonner'

export default function OnboardingPage() {
  const handleOnboardingComplete = (data: any) => {
    console.log('Onboarding completed with data:', data)
    
    // Save onboarding data (in a real app, this would be an API call)
    localStorage.setItem('hvppy_onboarding_data', JSON.stringify(data))
    
    toast.success('Welcome to HVPPY Central! 🎉')
    
    // Redirect to main social feed
    setTimeout(() => {
      window.location.href = '/social'
    }, 1500)
  }

  return (
    <SocialOnboarding onComplete={handleOnboardingComplete} />
  )
}
