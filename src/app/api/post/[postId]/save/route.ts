import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function POST(req: Request, { params }: { params: { postId: string } }) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { postId } = params

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 })
    }

    // Check if the post exists
    const post = await prisma.post.findUnique({ where: { id: postId } })
    if (!post) {
      return new NextResponse("Post not found", { status: 404 })
    }

    // Check if already saved
    const existingSave = await prisma.save.findUnique({
      where: {
        userId_postId: {
          userId: session.user.id as string,
          postId: postId,
        },
      },
    })

    if (existingSave) {
      return new NextResponse("Post already saved", { status: 409 })
    }

    // Create the save record
    await prisma.save.create({
      data: {
        userId: session.user.id as string,
        postId: postId,
      },
    })

    return NextResponse.json({ saved: true, message: "Post saved successfully" })
  } catch (error) {
    console.error("Error saving post:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}

export async function DELETE(req: Request, { params }: { params: { postId: string } }) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { postId } = params

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 })
    }

    // Check if the save exists
    const existingSave = await prisma.save.findUnique({
      where: {
        userId_postId: {
          userId: session.user.id as string,
          postId: postId,
        },
      },
    })

    if (!existingSave) {
      return new NextResponse("Post not saved by this user", { status: 404 })
    }

    // Delete the save record
    await prisma.save.delete({
      where: {
        userId_postId: {
          userId: session.user.id as string,
          postId: postId,
        },
      },
    })

    return NextResponse.json({ saved: false, message: "Post removed from saved" })
  } catch (error) {
    console.error("Error removing saved post:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
