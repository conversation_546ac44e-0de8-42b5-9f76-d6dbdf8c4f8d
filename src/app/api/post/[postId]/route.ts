import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { ModerationStatus, TranscodingStatus } from "@prisma/client";

export async function GET(req: Request, { params }: { params: { postId: string } }) {
  try {
    const { postId } = params;
    const session = await auth();
    const userId = session?.user?.id;

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 });
    }

    const post = await prisma.post.findUnique({
      where: { id: postId },
      include: {
        user: { select: { id: true, name: true, email: true, image: true, displayName: true, username: true } },
        creator: { select: { id: true, stageName: true } },
        likes: userId ? { // Conditionally include likes if user is logged in
          where: { userId: userId as string },
          select: { userId: true },
        } : false,
      },
    });

    if (!post) {
      return new NextResponse("Post not found", { status: 404 });
    }

    // Add hasLiked property
    const postWithLikeStatus = {
      ...post,
      hasLiked: post.likes ? post.likes.length > 0 : false,
      likes: undefined, // Remove the raw likes array from the response
    };

    return NextResponse.json({ post: postWithLikeStatus });
  } catch (error) {
    console.error("Error fetching single post:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
