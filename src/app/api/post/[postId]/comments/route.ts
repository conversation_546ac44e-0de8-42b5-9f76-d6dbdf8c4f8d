import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";

export async function GET(req: Request, { params }: { params: { postId: string } }) {
  try {
    const { postId } = params;

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 });
    }

    const comments = await prisma.comment.findMany({
      where: { postId: postId },
      include: {
        user: { select: { id: true, name: true, image: true, displayName: true, username: true } },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json({ comments });
  } catch (error) {
    console.error("Error fetching comments:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function POST(req: Request, { params }: { params: { postId: string } }) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId } = params;
    const { content } = await req.json();

    if (!postId || !content) {
      return new NextResponse("Post ID and content are required", { status: 400 });
    }

    // Check if the post exists
    const post = await prisma.post.findUnique({ where: { id: postId } });
    if (!post) {
      return new NextResponse("Post not found", { status: 404 });
    }

    const comment = await prisma.comment.create({
      data: {
        userId: session.user.id as string,
        postId: postId,
        content: content,
      },
      include: {
        user: { select: { id: true, name: true, image: true, displayName: true, username: true } },
      },
    });

    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error("Error adding comment:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
