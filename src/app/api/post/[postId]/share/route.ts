import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: { postId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId } = params;

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return new NextResponse("Post not found", { status: 404 });
    }

    // Increment share count
    await prisma.post.update({
      where: { id: postId },
      data: {
        shareCount: {
          increment: 1,
        },
      },
    });

    // TODO: Add share tracking/analytics here if needed
    // await prisma.shareEvent.create({
    //   data: {
    //     userId: session.user.id,
    //     postId: postId,
    //     platform: 'native', // or get from request body
    //   },
    // });

    return NextResponse.json({
      success: true,
      shareCount: post.shareCount + 1
    });
  } catch (error) {
    console.error("Error tracking share:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}