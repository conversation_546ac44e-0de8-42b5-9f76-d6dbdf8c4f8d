import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(
  request: NextRequest,
  { params }: { params: { postId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId } = params;

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return new NextResponse("Post not found", { status: 404 });
    }

    // Check if memory already exists
    const existingMemory = await prisma.memory.findUnique({
      where: {
        userId_postId: {
          userId: session.user.id,
          postId: postId,
        },
      },
    });

    let saved = false;

    if (existingMemory) {
      // Remove memory
      await prisma.memory.delete({
        where: { id: existingMemory.id },
      });
      saved = false;
    } else {
      // Add memory
      await prisma.memory.create({
        data: {
          userId: session.user.id,
          postId: postId,
        },
      });
      saved = true;
    }

    return NextResponse.json({ saved });
  } catch (error) {
    console.error("Error toggling memory:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}