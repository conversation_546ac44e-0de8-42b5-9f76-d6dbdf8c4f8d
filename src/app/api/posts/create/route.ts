import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"
import { storage } from "@/lib/appwrite"
import { ID } from "appwrite"

export async function POST(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const formData = await req.formData()
    
    const title = formData.get('title') as string
    const content = formData.get('content') as string
    const contentType = formData.get('contentType') as string
    const moodsStr = formData.get('moods') as string
    const visibility = formData.get('visibility') as string
    const isDraftStr = formData.get('isDraft') as string
    const scheduledDateStr = formData.get('scheduledDate') as string

    if (!content?.trim()) {
      return new NextResponse("Content is required", { status: 400 })
    }

    const moods = moodsStr ? JSON.parse(moodsStr) : []
    const isDraft = isDraftStr === 'true'
    const scheduledDate = scheduledDateStr ? new Date(scheduledDateStr) : null

    // Handle file uploads
    const files: File[] = []
    const fileKeys = Array.from(formData.keys()).filter(key => key.startsWith('file_'))
    
    for (const key of fileKeys) {
      const file = formData.get(key) as File
      if (file) {
        files.push(file)
      }
    }

    let contentUrl: string | null = null
    let thumbnailUrl: string | null = null
    const mediaUrls: string[] = []

    // Upload files to Appwrite Storage
    if (files.length > 0) {
      try {
        for (const file of files) {
          const fileId = ID.unique()
          const arrayBuffer = await file.arrayBuffer()
          const buffer = new Uint8Array(arrayBuffer)
          
          const uploadedFile = await storage.createFile(
            process.env.APPWRITE_BUCKET_ID!,
            fileId,
            buffer,
            [
              `read("user:${session.user.id}")`,
              `write("user:${session.user.id}")`,
            ]
          )

          const fileUrl = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${process.env.APPWRITE_BUCKET_ID}/files/${uploadedFile.$id}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`
          
          mediaUrls.push(fileUrl)
          
          // Set primary content URL for the first file
          if (!contentUrl) {
            contentUrl = fileUrl
            
            // For images and videos, use the same URL as thumbnail
            if (contentType === 'IMAGE' || contentType === 'VIDEO') {
              thumbnailUrl = fileUrl
            }
          }
        }
      } catch (error) {
        console.error('Error uploading files:', error)
        return new NextResponse("Failed to upload files", { status: 500 })
      }
    }

    // Create the post
    const post = await prisma.post.create({
      data: {
        userId: session.user.id,
        title: title || null,
        content,
        contentType,
        contentUrl,
        thumbnailUrl,
        mediaUrls: mediaUrls.length > 0 ? mediaUrls : null,
        mood: moods.length > 0 ? moods : null,
        visibility: visibility || 'public',
        isDraft,
        scheduledAt: scheduledDate,
        publishedAt: isDraft || scheduledDate ? null : new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            isVerified: true,
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
          },
        },
      },
    })

    // Create notifications for followers (only for published posts)
    if (!isDraft && !scheduledDate) {
      try {
        // Get user's followers
        const followers = await prisma.follow.findMany({
          where: {
            followingId: session.user.id,
          },
          select: {
            followerId: true,
          },
        })

        // Create notifications for followers
        if (followers.length > 0) {
          const notifications = followers.map(follow => ({
            userId: follow.followerId,
            fromUserId: session.user.id,
            postId: post.id,
            type: 'new_post',
            message: `${session.user.name || session.user.email} shared a new post`,
            metadata: {
              contentType,
              moods,
            },
          }))

          await prisma.notification.createMany({
            data: notifications,
          })
        }
      } catch (error) {
        console.error('Error creating notifications:', error)
        // Don't fail the post creation if notifications fail
      }
    }

    // Transform the response
    const transformedPost = {
      id: post.id,
      title: post.title,
      content: post.content,
      contentType: post.contentType,
      contentUrl: post.contentUrl,
      thumbnailUrl: post.thumbnailUrl,
      mediaUrls: post.mediaUrls,
      mood: post.mood,
      visibility: post.visibility,
      isDraft: post.isDraft,
      scheduledAt: post.scheduledAt,
      publishedAt: post.publishedAt,
      createdAt: post.createdAt,
      likeCount: post._count.likes,
      commentCount: post._count.comments,
      shareCount: post.shareCount,
      user: {
        id: post.user.id,
        username: post.user.username,
        displayName: post.user.displayName,
        avatarUrl: post.user.avatar,
        isVerified: post.user.isVerified,
      },
    }

    return NextResponse.json({
      success: true,
      post: transformedPost,
      message: isDraft ? 'Draft saved successfully' : 
               scheduledDate ? 'Post scheduled successfully' : 
               'Post published successfully',
    })
  } catch (error) {
    console.error("Error creating post:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
