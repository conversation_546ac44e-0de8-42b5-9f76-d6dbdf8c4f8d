import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const userId = searchParams.get("userId") || session.user.id
    const page = parseInt(searchParams.get("page") || "0")
    const limit = parseInt(searchParams.get("limit") || "20")

    // Get followers for the specified user
    const followers = await prisma.follow.findMany({
      where: {
        followingId: userId,
      },
      include: {
        follower: {
          select: {
            id: true,
            username: true,
            displayName: true,
            bio: true,
            avatar: true,
            isVerified: true,
            createdAt: true,
            _count: {
              select: {
                followers: true,
                following: true,
                posts: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: page * limit,
      take: limit,
    })

    // Transform the data
    const followersData = followers.map((follow) => ({
      id: follow.follower.id,
      username: follow.follower.username,
      displayName: follow.follower.displayName,
      bio: follow.follower.bio,
      avatar: follow.follower.avatar,
      isVerified: follow.follower.isVerified,
      followerCount: follow.follower._count.followers,
      followingCount: follow.follower._count.following,
      postCount: follow.follower._count.posts,
      createdAt: follow.follower.createdAt,
      followedAt: follow.createdAt,
    }))

    // Get total count for pagination
    const totalCount = await prisma.follow.count({
      where: {
        followingId: userId,
      },
    })

    return NextResponse.json({
      followers: followersData,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: (page + 1) * limit < totalCount,
      },
    })
  } catch (error) {
    console.error("Error fetching followers:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
