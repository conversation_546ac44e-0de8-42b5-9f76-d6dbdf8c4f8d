import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request, { params }: { params: { username: string } }) {
  try {
    const session = await auth()
    const { username } = params

    if (!username) {
      return new NextResponse("Username is required", { status: 400 })
    }

    // Find user by username
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { id: username }, // Allow lookup by ID as fallback
        ],
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        bio: true,
        avatar: true,
        isVerified: true,
        createdAt: true,
        _count: {
          select: {
            followers: true,
            following: true,
            posts: true,
          },
        },
      },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    // Check if this is the current user's own profile
    const isOwnProfile = session?.user?.id === user.id

    // Check follow status if not own profile
    let isFollowing = false
    let isFollowedBy = false

    if (session?.user?.id && !isOwnProfile) {
      const followStatus = await prisma.follow.findMany({
        where: {
          OR: [
            {
              followerId: session.user.id,
              followingId: user.id,
            },
            {
              followerId: user.id,
              followingId: session.user.id,
            },
          ],
        },
      })

      isFollowing = followStatus.some(
        (follow) => follow.followerId === session.user.id && follow.followingId === user.id
      )
      isFollowedBy = followStatus.some(
        (follow) => follow.followerId === user.id && follow.followingId === session.user.id
      )
    }

    const profile = {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      bio: user.bio,
      avatarUrl: user.avatar,
      isVerified: user.isVerified,
      joinedAt: user.createdAt,
      followerCount: user._count.followers,
      followingCount: user._count.following,
      postCount: user._count.posts,
      isOwnProfile,
      isFollowing,
      isFollowedBy,
    }

    return NextResponse.json({ profile })
  } catch (error) {
    console.error("Error fetching user profile:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
