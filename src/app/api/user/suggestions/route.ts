import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const limit = parseInt(searchParams.get("limit") || "10")

    // Get users that the current user is already following
    const currentFollowing = await prisma.follow.findMany({
      where: {
        followerId: session.user.id,
      },
      select: {
        followingId: true,
      },
    })

    const followingIds = currentFollowing.map(f => f.followingId)
    followingIds.push(session.user.id) // Exclude self

    // Algorithm 1: Users followed by people you follow (friends of friends)
    const friendsOfFriends = await prisma.user.findMany({
      where: {
        id: {
          notIn: followingIds,
        },
        followers: {
          some: {
            followerId: {
              in: followingIds.filter(id => id !== session.user.id),
            },
          },
        },
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        bio: true,
        avatar: true,
        isVerified: true,
        createdAt: true,
        _count: {
          select: {
            followers: true,
            following: true,
            posts: true,
          },
        },
      },
      take: Math.ceil(limit * 0.6), // 60% of suggestions
    })

    // Algorithm 2: Popular creators (high follower count, recent activity)
    const popularCreators = await prisma.user.findMany({
      where: {
        id: {
          notIn: [...followingIds, ...friendsOfFriends.map(u => u.id)],
        },
        role: 'CREATOR',
        posts: {
          some: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        },
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        bio: true,
        avatar: true,
        isVerified: true,
        createdAt: true,
        _count: {
          select: {
            followers: true,
            following: true,
            posts: true,
          },
        },
      },
      orderBy: [
        { followers: { _count: 'desc' } },
        { posts: { _count: 'desc' } },
      ],
      take: Math.ceil(limit * 0.3), // 30% of suggestions
    })

    // Algorithm 3: New users (recently joined, to help them get discovered)
    const newUsers = await prisma.user.findMany({
      where: {
        id: {
          notIn: [
            ...followingIds, 
            ...friendsOfFriends.map(u => u.id),
            ...popularCreators.map(u => u.id)
          ],
        },
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        bio: true,
        avatar: true,
        isVerified: true,
        createdAt: true,
        _count: {
          select: {
            followers: true,
            following: true,
            posts: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: Math.ceil(limit * 0.1), // 10% of suggestions
    })

    // Combine and shuffle suggestions
    const allSuggestions = [
      ...friendsOfFriends,
      ...popularCreators,
      ...newUsers,
    ]

    // Transform the data
    const suggestions = allSuggestions.map((user) => ({
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      bio: user.bio,
      avatar: user.avatar,
      isVerified: user.isVerified,
      followerCount: user._count.followers,
      followingCount: user._count.following,
      postCount: user._count.posts,
      createdAt: user.createdAt,
    }))

    // Shuffle the suggestions to provide variety
    const shuffledSuggestions = suggestions
      .sort(() => Math.random() - 0.5)
      .slice(0, limit)

    return NextResponse.json({
      suggestions: shuffledSuggestions,
      algorithms: {
        friendsOfFriends: friendsOfFriends.length,
        popularCreators: popularCreators.length,
        newUsers: newUsers.length,
      },
    })
  } catch (error) {
    console.error("Error fetching user suggestions:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
