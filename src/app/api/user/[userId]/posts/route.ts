import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request, { params }: { params: { userId: string } }) {
  try {
    const session = await auth()
    const { userId } = params
    const { searchParams } = new URL(req.url)
    
    const tab = searchParams.get("tab") || "posts"
    const page = parseInt(searchParams.get("page") || "0")
    const limit = parseInt(searchParams.get("limit") || "10")

    if (!userId) {
      return new NextResponse("User ID is required", { status: 400 })
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    let posts = []

    switch (tab) {
      case "posts":
        // Get user's own posts
        posts = await prisma.post.findMany({
          where: {
            userId: userId,
          },
          select: {
            id: true,
            title: true,
            content: true,
            contentType: true,
            contentUrl: true,
            thumbnailUrl: true,
            createdAt: true,
            likeCount: true,
            shareCount: true,
            _count: {
              select: {
                comments: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          skip: page * limit,
          take: limit,
        })
        break

      case "media":
        // Get posts with media content
        posts = await prisma.post.findMany({
          where: {
            userId: userId,
            OR: [
              { contentType: 'VIDEO' },
              { contentType: 'IMAGE' },
              { contentType: 'MUSIC' },
            ],
          },
          select: {
            id: true,
            title: true,
            content: true,
            contentType: true,
            contentUrl: true,
            thumbnailUrl: true,
            createdAt: true,
            likeCount: true,
            shareCount: true,
            _count: {
              select: {
                comments: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          skip: page * limit,
          take: limit,
        })
        break

      case "likes":
        // Get posts liked by the user (only if viewing own profile or public)
        if (session?.user?.id === userId) {
          const likedPosts = await prisma.like.findMany({
            where: {
              userId: userId,
            },
            include: {
              post: {
                select: {
                  id: true,
                  title: true,
                  content: true,
                  contentType: true,
                  contentUrl: true,
                  thumbnailUrl: true,
                  createdAt: true,
                  likeCount: true,
                  shareCount: true,
                  user: {
                    select: {
                      id: true,
                      username: true,
                      displayName: true,
                      avatar: true,
                      isVerified: true,
                    },
                  },
                  _count: {
                    select: {
                      comments: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            skip: page * limit,
            take: limit,
          })

          posts = likedPosts.map(like => ({
            ...like.post,
            likedAt: like.createdAt,
          }))
        }
        break

      case "memories":
        // Get saved posts (memories) - only for own profile
        if (session?.user?.id === userId) {
          const savedPosts = await prisma.save.findMany({
            where: {
              userId: userId,
            },
            include: {
              post: {
                select: {
                  id: true,
                  title: true,
                  content: true,
                  contentType: true,
                  contentUrl: true,
                  thumbnailUrl: true,
                  createdAt: true,
                  likeCount: true,
                  shareCount: true,
                  user: {
                    select: {
                      id: true,
                      username: true,
                      displayName: true,
                      avatar: true,
                      isVerified: true,
                    },
                  },
                  _count: {
                    select: {
                      comments: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            skip: page * limit,
            take: limit,
          })

          posts = savedPosts.map(save => ({
            ...save.post,
            savedAt: save.createdAt,
          }))
        }
        break

      default:
        return new NextResponse("Invalid tab", { status: 400 })
    }

    // Transform posts data
    const transformedPosts = posts.map((post: any) => ({
      id: post.id,
      title: post.title,
      content: post.content,
      contentType: post.contentType,
      contentUrl: post.contentUrl,
      thumbnailUrl: post.thumbnailUrl,
      createdAt: post.createdAt,
      likeCount: post.likeCount,
      commentCount: post._count?.comments || 0,
      shareCount: post.shareCount,
      user: post.user || null,
      likedAt: post.likedAt || null,
      savedAt: post.savedAt || null,
    }))

    // Get total count for pagination
    let totalCount = 0
    switch (tab) {
      case "posts":
        totalCount = await prisma.post.count({
          where: { userId: userId },
        })
        break
      case "media":
        totalCount = await prisma.post.count({
          where: {
            userId: userId,
            OR: [
              { contentType: 'VIDEO' },
              { contentType: 'IMAGE' },
              { contentType: 'MUSIC' },
            ],
          },
        })
        break
      case "likes":
        if (session?.user?.id === userId) {
          totalCount = await prisma.like.count({
            where: { userId: userId },
          })
        }
        break
      case "memories":
        if (session?.user?.id === userId) {
          totalCount = await prisma.save.count({
            where: { userId: userId },
          })
        }
        break
    }

    return NextResponse.json({
      posts: transformedPosts,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: (page + 1) * limit < totalCount,
      },
    })
  } catch (error) {
    console.error("Error fetching user posts:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
