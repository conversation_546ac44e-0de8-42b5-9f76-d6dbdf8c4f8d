import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";

export async function GET(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const followerId = searchParams.get("followerId");
    const followingId = searchParams.get("followingId");

    if (!followerId || !followingId) {
      return new NextResponse("Missing followerId or followingId", { status: 400 });
    }

    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: followerId,
          followingId: followingId,
        },
      },
    });

    return NextResponse.json({ isFollowing: !!existingFollow });
  } catch (error) {
    console.error("Error checking follow status:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { followingId } = await req.json();

    if (!followingId) {
      return new NextResponse("Missing followingId", { status: 400 });
    }

    if (followingId === session.user.id) {
      return new NextResponse("Cannot follow yourself", { status: 400 });
    }

    // Check if user exists
    const userToFollow = await prisma.user.findUnique({
      where: { id: followingId },
    });

    if (!userToFollow) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Check if already following
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: session.user.id as string,
          followingId: followingId,
        },
      },
    });

    if (existingFollow) {
      return new NextResponse("Already following this user", { status: 409 });
    }

    // Create follow relationship
    await prisma.follow.create({
      data: {
        followerId: session.user.id as string,
        followingId: followingId,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Successfully followed user",
      user: {
        id: userToFollow.id,
        username: userToFollow.username,
        displayName: userToFollow.displayName,
        bio: userToFollow.bio,
        avatar: userToFollow.avatar,
        isVerified: userToFollow.isVerified,
        createdAt: userToFollow.createdAt,
      }
    });
  } catch (error) {
    console.error("Error following user:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { followingId } = await req.json();

    if (!followingId) {
      return new NextResponse("Missing followingId", { status: 400 });
    }

    // Check if follow relationship exists
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: session.user.id as string,
          followingId: followingId,
        },
      },
    });

    if (!existingFollow) {
      return new NextResponse("Not following this user", { status: 404 });
    }

    // Delete follow relationship
    await prisma.follow.delete({
      where: {
        followerId_followingId: {
          followerId: session.user.id as string,
          followingId: followingId,
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Successfully unfollowed user"
    });
  } catch (error) {
    console.error("Error unfollowing user:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}