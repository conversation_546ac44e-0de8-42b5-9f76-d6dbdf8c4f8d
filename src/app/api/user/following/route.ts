import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const userId = searchParams.get("userId") || session.user.id
    const page = parseInt(searchParams.get("page") || "0")
    const limit = parseInt(searchParams.get("limit") || "20")

    // Get users that the specified user is following
    const following = await prisma.follow.findMany({
      where: {
        followerId: userId,
      },
      include: {
        following: {
          select: {
            id: true,
            username: true,
            displayName: true,
            bio: true,
            avatar: true,
            isVerified: true,
            createdAt: true,
            _count: {
              select: {
                followers: true,
                following: true,
                posts: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: page * limit,
      take: limit,
    })

    // Transform the data
    const followingData = following.map((follow) => ({
      id: follow.following.id,
      username: follow.following.username,
      displayName: follow.following.displayName,
      bio: follow.following.bio,
      avatar: follow.following.avatar,
      isVerified: follow.following.isVerified,
      followerCount: follow.following._count.followers,
      followingCount: follow.following._count.following,
      postCount: follow.following._count.posts,
      createdAt: follow.following.createdAt,
      followedAt: follow.createdAt,
    }))

    // Get total count for pagination
    const totalCount = await prisma.follow.count({
      where: {
        followerId: userId,
      },
    })

    return NextResponse.json({
      following: followingData,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: (page + 1) * limit < totalCount,
      },
    })
  } catch (error) {
    console.error("Error fetching following:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
