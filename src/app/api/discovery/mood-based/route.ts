import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function POST(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { moods, limit = 20 } = await req.json()

    if (!moods || !Array.isArray(moods) || moods.length === 0) {
      return new NextResponse("Moods array is required", { status: 400 })
    }

    // Get posts that match the specified moods
    const posts = await prisma.post.findMany({
      where: {
        AND: [
          {
            OR: [
              { mood: { hasSome: moods } }, // Posts with any of the specified moods
              { 
                // Also include posts where content analysis suggests mood match
                content: {
                  contains: moods.join(' '),
                  mode: 'insensitive'
                }
              }
            ]
          },
          { isDraft: false },
          { publishedAt: { not: null } },
          {
            // Exclude blocked users and content
            user: {
              id: {
                notIn: await getUserBlockedIds(session.user.id)
              }
            }
          }
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            isVerified: true,
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
          },
        },
      },
      orderBy: [
        // Prioritize recent content
        { publishedAt: 'desc' },
        // Then by engagement
        { likeCount: 'desc' },
      ],
      take: limit * 2, // Get more to allow for filtering
    })

    // Score posts based on mood matching
    const scoredPosts = posts.map(post => {
      let moodScore = 0
      
      // Direct mood matches
      if (post.mood) {
        const postMoods = Array.isArray(post.mood) ? post.mood : [post.mood]
        const matchingMoods = postMoods.filter((mood: string) => 
          moods.some((requestedMood: string) => 
            mood.toLowerCase().includes(requestedMood.toLowerCase())
          )
        )
        moodScore += matchingMoods.length * 0.5
      }
      
      // Content-based mood inference
      const contentLower = post.content.toLowerCase()
      moods.forEach((mood: string) => {
        if (contentLower.includes(mood.toLowerCase())) {
          moodScore += 0.2
        }
      })
      
      // Engagement boost
      const engagementScore = (post.likeCount + post._count.comments * 2) / 100
      
      // Recency boost (newer content gets higher score)
      const daysSincePublished = post.publishedAt 
        ? (Date.now() - new Date(post.publishedAt).getTime()) / (1000 * 60 * 60 * 24)
        : 999
      const recencyScore = Math.max(0, 1 - daysSincePublished / 7) // Decay over 7 days
      
      return {
        ...post,
        moodScore: moodScore + engagementScore * 0.3 + recencyScore * 0.2
      }
    })

    // Sort by mood score and take the top results
    const topPosts = scoredPosts
      .sort((a, b) => b.moodScore - a.moodScore)
      .slice(0, limit)

    // Transform to FeedItem format
    const content = topPosts.map(post => ({
      id: post.id,
      type: 'post' as const,
      post: {
        id: post.id,
        title: post.title,
        content: post.content,
        contentType: post.contentType,
        contentUrl: post.contentUrl,
        thumbnailUrl: post.thumbnailUrl,
        mediaUrls: post.mediaUrls,
        mood: post.mood,
        createdAt: post.createdAt,
        publishedAt: post.publishedAt,
        likeCount: post.likeCount,
        commentCount: post._count.comments,
        shareCount: post.shareCount,
        user: {
          id: post.user.id,
          username: post.user.username,
          displayName: post.user.displayName,
          avatarUrl: post.user.avatar,
          isVerified: post.user.isVerified,
        },
      },
      metadata: {
        moodScore: post.moodScore,
        matchingMoods: moods,
        algorithm: 'mood_based',
      },
    }))

    return NextResponse.json({
      content,
      metadata: {
        requestedMoods: moods,
        totalFound: posts.length,
        returned: content.length,
        algorithm: 'mood_based',
      },
    })
  } catch (error) {
    console.error("Error fetching mood-based content:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}

async function getUserBlockedIds(userId: string): Promise<string[]> {
  try {
    // This would get blocked users from user preferences
    // For now, return empty array
    return []
  } catch (error) {
    console.error("Error fetching blocked users:", error)
    return []
  }
}
