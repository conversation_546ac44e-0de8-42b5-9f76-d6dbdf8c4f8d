import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const timeframe = searchParams.get("timeframe") || "day"
    const limit = parseInt(searchParams.get("limit") || "20")

    // Calculate time threshold based on timeframe
    const now = new Date()
    let timeThreshold: Date

    switch (timeframe) {
      case 'hour':
        timeThreshold = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case 'week':
        timeThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'day':
      default:
        timeThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
    }

    // Get trending posts based on engagement velocity
    const posts = await prisma.post.findMany({
      where: {
        AND: [
          { isDraft: false },
          { publishedAt: { not: null, gte: timeThreshold } },
          {
            // Exclude blocked users
            user: {
              id: {
                notIn: await getUserBlockedIds(session.user.id)
              }
            }
          }
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            isVerified: true,
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
          },
        },
        // Get recent interactions for velocity calculation
        likes: {
          where: {
            createdAt: { gte: timeThreshold }
          },
          select: {
            createdAt: true
          }
        },
        comments: {
          where: {
            createdAt: { gte: timeThreshold }
          },
          select: {
            createdAt: true
          }
        }
      },
      orderBy: [
        { publishedAt: 'desc' }
      ],
      take: limit * 3, // Get more to calculate trending scores
    })

    // Calculate trending scores
    const trendingPosts = posts.map(post => {
      const hoursSincePublished = post.publishedAt 
        ? (Date.now() - new Date(post.publishedAt).getTime()) / (1000 * 60 * 60)
        : 999

      // Engagement velocity (interactions per hour)
      const recentLikes = post.likes.length
      const recentComments = post.comments.length
      const totalRecentEngagement = recentLikes + (recentComments * 2) // Comments worth more
      
      const engagementVelocity = hoursSincePublished > 0 
        ? totalRecentEngagement / hoursSincePublished 
        : totalRecentEngagement

      // Total engagement score
      const totalEngagement = post.likeCount + (post._count.comments * 2) + (post.shareCount * 3)
      
      // Recency boost (newer content gets higher score)
      const recencyMultiplier = Math.max(0.1, 1 - (hoursSincePublished / 24))
      
      // Viral coefficient (how much engagement is accelerating)
      const viralCoefficient = engagementVelocity > 0 
        ? Math.min(2, totalRecentEngagement / Math.max(1, totalEngagement - totalRecentEngagement))
        : 1

      // Final trending score
      const trendingScore = (
        engagementVelocity * 0.4 +
        totalEngagement * 0.3 +
        recencyMultiplier * 0.2 +
        viralCoefficient * 0.1
      )

      return {
        ...post,
        trendingScore,
        engagementVelocity,
        recentEngagement: totalRecentEngagement,
        viralCoefficient
      }
    })

    // Sort by trending score and take top results
    const topTrending = trendingPosts
      .sort((a, b) => b.trendingScore - a.trendingScore)
      .slice(0, limit)

    // Transform to FeedItem format
    const content = topTrending.map(post => ({
      id: post.id,
      type: 'post' as const,
      post: {
        id: post.id,
        title: post.title,
        content: post.content,
        contentType: post.contentType,
        contentUrl: post.contentUrl,
        thumbnailUrl: post.thumbnailUrl,
        mediaUrls: post.mediaUrls,
        mood: post.mood,
        createdAt: post.createdAt,
        publishedAt: post.publishedAt,
        likeCount: post.likeCount,
        commentCount: post._count.comments,
        shareCount: post.shareCount,
        user: {
          id: post.user.id,
          username: post.user.username,
          displayName: post.user.displayName,
          avatarUrl: post.user.avatar,
          isVerified: post.user.isVerified,
        },
      },
      metadata: {
        trendingScore: post.trendingScore,
        engagementVelocity: post.engagementVelocity,
        recentEngagement: post.recentEngagement,
        viralCoefficient: post.viralCoefficient,
        timeframe,
        algorithm: 'trending',
      },
    }))

    return NextResponse.json({
      content,
      metadata: {
        timeframe,
        totalAnalyzed: posts.length,
        returned: content.length,
        algorithm: 'trending',
        timeThreshold: timeThreshold.toISOString(),
      },
    })
  } catch (error) {
    console.error("Error fetching trending content:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}

async function getUserBlockedIds(userId: string): Promise<string[]> {
  try {
    // This would get blocked users from user preferences
    // For now, return empty array
    return []
  } catch (error) {
    console.error("Error fetching blocked users:", error)
    return []
  }
}
