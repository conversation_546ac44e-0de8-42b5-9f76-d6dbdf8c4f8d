import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function GET(req: Request) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const page = parseInt(searchParams.get("page") || "0")
    const limit = parseInt(searchParams.get("limit") || "20")
    const unreadOnly = searchParams.get("unreadOnly") === "true"

    // Get notifications for the current user
    const notifications = await prisma.notification.findMany({
      where: {
        userId: session.user.id,
        ...(unreadOnly && { isRead: false }),
      },
      include: {
        fromUser: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            isVerified: true,
          },
        },
        post: {
          select: {
            id: true,
            title: true,
            content: true,
            thumbnailUrl: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: page * limit,
      take: limit,
    })

    // Transform notifications
    const transformedNotifications = notifications.map((notification) => ({
      id: notification.id,
      type: notification.type,
      message: notification.message,
      isRead: notification.isRead,
      createdAt: notification.createdAt,
      fromUser: {
        id: notification.fromUser.id,
        username: notification.fromUser.username,
        displayName: notification.fromUser.displayName,
        avatarUrl: notification.fromUser.avatar,
        isVerified: notification.fromUser.isVerified,
      },
      post: notification.post ? {
        id: notification.post.id,
        title: notification.post.title,
        content: notification.post.content,
        thumbnailUrl: notification.post.thumbnailUrl,
      } : null,
      metadata: notification.metadata,
    }))

    // Get total count
    const totalCount = await prisma.notification.count({
      where: {
        userId: session.user.id,
        ...(unreadOnly && { isRead: false }),
      },
    })

    // Get unread count
    const unreadCount = await prisma.notification.count({
      where: {
        userId: session.user.id,
        isRead: false,
      },
    })

    return NextResponse.json({
      notifications: transformedNotifications,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: (page + 1) * limit < totalCount,
      },
      unreadCount,
    })
  } catch (error) {
    console.error("Error fetching notifications:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
