import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function POST(req: Request, { params }: { params: { notificationId: string } }) {
  try {
    const session = await auth()

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { notificationId } = params

    if (!notificationId) {
      return new NextResponse("Notification ID is required", { status: 400 })
    }

    // Check if notification exists and belongs to the user
    const notification = await prisma.notification.findUnique({
      where: {
        id: notificationId,
      },
    })

    if (!notification) {
      return new NextResponse("Notification not found", { status: 404 })
    }

    if (notification.userId !== session.user.id) {
      return new NextResponse("Forbidden", { status: 403 })
    }

    // Mark as read
    await prisma.notification.update({
      where: {
        id: notificationId,
      },
      data: {
        isRead: true,
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error marking notification as read:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
