"use client"

import React, { useState } from 'react'
import { SocialLayout } from '@/components/layout/social-layout'
import { UserSuggestions } from '@/components/social/user-suggestions'
import { FollowButton } from '@/components/social/follow-button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Users, 
  Search, 
  Filter, 
  Star,
  TrendingUp,
  Heart,
  Play,
  Music,
  Camera,
  Palette,
  Video,
  Verified,
  MapPin,
  Calendar
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function CreatorsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')

  const categories = [
    { id: 'all', label: 'All Creators', icon: Users, count: '50K+' },
    { id: 'trending', label: 'Trending', icon: TrendingUp, count: '2.1K' },
    { id: 'music', label: 'Musicians', icon: Music, count: '12K' },
    { id: 'art', label: 'Artists', icon: Palette, count: '8.5K' },
    { id: 'photo', label: 'Photographers', icon: Camera, count: '15K' },
    { id: 'video', label: 'Video Creators', icon: Video, count: '9.2K' },
  ]

  const featuredCreators = [
    {
      id: '1',
      username: 'alexmusic',
      displayName: 'Alex Rivera',
      bio: 'Electronic music producer & DJ from LA. Creating vibes that move your soul 🎵',
      avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      isVerified: true,
      followers: 125000,
      following: 892,
      posts: 234,
      category: 'music',
      location: 'Los Angeles, CA',
      joinedDate: '2023-01-15',
      mood: ['energetic', 'creative'],
    },
    {
      id: '2',
      username: 'sarahvisuals',
      displayName: 'Sarah Chen',
      bio: 'Visual artist exploring the intersection of technology and emotion through digital art 🎨',
      avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      isVerified: true,
      followers: 89000,
      following: 456,
      posts: 189,
      category: 'art',
      location: 'New York, NY',
      joinedDate: '2022-11-08',
      mood: ['creative', 'mysterious'],
    },
    {
      id: '3',
      username: 'mikephoto',
      displayName: 'Mike Johnson',
      bio: 'Street photographer capturing the beauty in everyday moments 📸',
      avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      isVerified: false,
      followers: 45000,
      following: 234,
      posts: 567,
      category: 'photo',
      location: 'Chicago, IL',
      joinedDate: '2023-03-22',
      mood: ['nostalgic', 'peaceful'],
    },
  ]

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getMoodColor = (mood: string) => {
    const colors: Record<string, string> = {
      energetic: 'bg-orange-100 text-orange-700',
      creative: 'bg-purple-100 text-purple-700',
      mysterious: 'bg-indigo-100 text-indigo-700',
      nostalgic: 'bg-amber-100 text-amber-700',
      peaceful: 'bg-blue-100 text-blue-700',
    }
    return colors[mood] || 'bg-gray-100 text-gray-700'
  }

  return (
    <SocialLayout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-6">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  Creators
                </h1>
                <p className="text-muted-foreground mt-2">
                  Discover amazing creators and connect with your community
                </p>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="relative flex-1 lg:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search creators..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </div>
            </div>

            {/* Category Tabs */}
            <Tabs value={activeCategory} onValueChange={setActiveCategory}>
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
                {categories.map((category) => {
                  const Icon = category.icon
                  return (
                    <TabsTrigger
                      key={category.id}
                      value={category.id}
                      className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white"
                    >
                      <Icon className="h-4 w-4" />
                      <span className="hidden sm:inline">{category.label}</span>
                      <Badge variant="secondary" className="ml-1 text-xs hidden lg:inline">
                        {category.count}
                      </Badge>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </Tabs>
          </motion.div>

          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Main Content */}
            <div className="xl:col-span-3 space-y-6">
              {/* Featured Creators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-yellow-500" />
                      Featured Creators
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {featuredCreators.map((creator, index) => (
                        <motion.div
                          key={creator.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 + index * 0.1 }}
                        >
                          <Card className="h-full hover:shadow-lg transition-shadow">
                            <CardContent className="p-6">
                              <div className="text-center space-y-4">
                                <div className="relative">
                                  <Avatar className="h-20 w-20 mx-auto border-4 border-purple-100">
                                    <AvatarImage src={creator.avatarUrl} alt={creator.displayName} />
                                    <AvatarFallback>{creator.displayName.charAt(0)}</AvatarFallback>
                                  </Avatar>
                                  {creator.isVerified && (
                                    <div className="absolute -bottom-1 -right-1 bg-blue-500 rounded-full p-1">
                                      <Verified className="h-3 w-3 text-white" />
                                    </div>
                                  )}
                                </div>
                                
                                <div>
                                  <h3 className="font-semibold text-lg">{creator.displayName}</h3>
                                  <p className="text-sm text-muted-foreground">@{creator.username}</p>
                                </div>
                                
                                <p className="text-sm text-center line-clamp-3">{creator.bio}</p>
                                
                                <div className="flex justify-center gap-1">
                                  {creator.mood.map((mood) => (
                                    <Badge key={mood} variant="outline" className={getMoodColor(mood)}>
                                      {mood}
                                    </Badge>
                                  ))}
                                </div>
                                
                                <div className="grid grid-cols-3 gap-4 text-center">
                                  <div>
                                    <div className="font-semibold">{formatNumber(creator.followers)}</div>
                                    <div className="text-xs text-muted-foreground">Followers</div>
                                  </div>
                                  <div>
                                    <div className="font-semibold">{formatNumber(creator.following)}</div>
                                    <div className="text-xs text-muted-foreground">Following</div>
                                  </div>
                                  <div>
                                    <div className="font-semibold">{creator.posts}</div>
                                    <div className="text-xs text-muted-foreground">Posts</div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                                  <MapPin className="h-3 w-3" />
                                  <span>{creator.location}</span>
                                </div>
                                
                                <FollowButton
                                  userId={creator.id}
                                  username={creator.username}
                                  displayName={creator.displayName}
                                  className="w-full"
                                />
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* All Creators Grid */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>All Creators</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {Array.from({ length: 12 }, (_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.2 + i * 0.05 }}
                        >
                          <Card className="hover:shadow-md transition-shadow cursor-pointer">
                            <CardContent className="p-4 text-center space-y-3">
                              <Avatar className="h-16 w-16 mx-auto">
                                <AvatarImage src={`https://images.unsplash.com/photo-${1500000000000 + i}?w=100&h=100&fit=crop&crop=face`} />
                                <AvatarFallback>U{i + 1}</AvatarFallback>
                              </Avatar>
                              
                              <div>
                                <h4 className="font-medium">Creator {i + 1}</h4>
                                <p className="text-sm text-muted-foreground">@creator{i + 1}</p>
                              </div>
                              
                              <div className="text-xs text-muted-foreground">
                                {formatNumber(Math.floor(Math.random() * 100000))} followers
                              </div>
                              
                              <FollowButton
                                userId={`creator-${i + 1}`}
                                username={`creator${i + 1}`}
                                displayName={`Creator ${i + 1}`}
                                size="sm"
                                className="w-full"
                              />
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="xl:col-span-1 space-y-6"
            >
              {/* Trending Creators */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-orange-500" />
                    Trending Now
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Array.from({ length: 5 }, (_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <div className="text-sm font-medium text-muted-foreground w-6">
                        #{i + 1}
                      </div>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={`https://images.unsplash.com/photo-${1600000000000 + i}?w=50&h=50&fit=crop&crop=face`} />
                        <AvatarFallback>T{i + 1}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">Trending Creator {i + 1}</p>
                        <p className="text-xs text-muted-foreground">+{Math.floor(Math.random() * 1000)} followers</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Creator Categories */}
              <Card>
                <CardHeader>
                  <CardTitle>Categories</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {categories.slice(2).map((category) => {
                    const Icon = category.icon
                    return (
                      <div key={category.id} className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 cursor-pointer">
                        <div className="flex items-center gap-3">
                          <Icon className="h-4 w-4 text-purple-500" />
                          <span className="text-sm font-medium">{category.label}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {category.count}
                        </Badge>
                      </div>
                    )
                  })}
                </CardContent>
              </Card>

              {/* Suggestions */}
              <UserSuggestions
                title="Suggested for You"
                limit={5}
                variant="card"
                showStats={true}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </SocialLayout>
  )
}
