"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Link from "next/link";
import CommentsSection from "@/components/comments-section";

interface Post {
  id: string;
  title: string;
  content: string;
  contentType: string;
  contentUrl: string; // Appwrite file ID
  mediaUrls: string[];
  thumbnailUrl?: string;
  transcodingStatus: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  renditions?: { [key: string]: string }; // URLs of different transcoded versions
  moderationStatus: "PENDING" | "APPROVED" | "REJECTED";
  moderationNotes?: string;
  createdAt: string;
  likeCount: number; // Added for likes
  hasLiked: boolean; // Added for current user's like status
  user: { id: string; name: string | null; email: string | null; image: string | null; displayName: string | null; username: string | null };
  creator: { id: string; stageName: string | null } | null;
}

export default function PostDetailPage() {
  const { postId } = useParams();
  const { data: session } = useSession();
  const [post, setPost] = useState<Post | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPost = async () => {
    try {
      const response = await fetch(`/api/post/${postId}`);
      if (response.ok) {
        const data = await response.json();
        setPost(data.post);
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Failed to fetch post.");
        toast.error(errorData.message || "Failed to fetch post.");
      }
    } catch (err) {
      console.error("Error fetching post:", err);
      setError("An unexpected error occurred while fetching the post.");
      toast.error("An unexpected error occurred while fetching the post.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (postId) {
      fetchPost();
    }
  }, [postId, session]); // Re-fetch if postId or session changes

  const handleLike = async () => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to like a post.");
      return;
    }

    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      if (response.ok) {
        toast.success("Post liked!");
        setPost(prevPost => prevPost ? { ...prevPost, likeCount: prevPost.likeCount + 1, hasLiked: true } : null);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to like post.");
      }
    } catch (error) {
      console.error("Error liking post:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  const handleUnlike = async () => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to unlike a post.");
      return;
    }

    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
      });

      if (response.ok) {
        toast.success("Post unliked!");
        setPost(prevPost => prevPost ? { ...prevPost, likeCount: prevPost.likeCount - 1, hasLiked: false } : null);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to unlike post.");
      }
    } catch (error) {
      console.error("Error unliking post:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading post...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Post not found.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Card className="w-full max-w-3xl mx-auto shadow-lg rounded-lg overflow-hidden">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">{post.title}</CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            By {" "}
            <Link href={`/profile/${post.user.id}`} className="hover:underline">
              {post.creator?.stageName || post.user.displayName || post.user.name || post.user.username || "Unknown User"}
            </Link>
            <span className="ml-2">• {new Date(post.createdAt).toLocaleDateString()}</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 pt-0">
          {post.moderationStatus === "REJECTED" ? (
            <div className="text-center text-red-500 py-8">
              <p className="font-bold text-lg">Content Rejected</p>
              {post.moderationNotes && <p className="text-sm">Reason: {post.moderationNotes}</p>}
            </div>
          ) : (
            <>
              {post.transcodingStatus === "PENDING" && (
                <div className="text-center text-yellow-500 py-8">Transcoding Pending...</div>
              )}
              {post.transcodingStatus === "IN_PROGRESS" && (
                <div className="text-center text-blue-500 py-8">Transcoding In Progress...</div>
              )}
              {post.transcodingStatus === "FAILED" && (
                <div className="text-center text-red-500 py-8">Transcoding Failed</div>
              )}
              {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("video/") && (
                <video controls src={post.renditions.high} className="w-full max-h-[60vh] object-contain rounded-md"></video>
              )}
              {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("audio/") && (
                <audio controls src={post.renditions.high} className="w-full"></audio>
              )}
              {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                <img src={`/api/media/stream/${post.contentUrl}`} alt={post.title} className="w-full max-h-[60vh] object-contain rounded-md" />
              )}
              {post.transcodingStatus === "COMPLETED" && !post.contentType.startsWith("image/") && !post.contentType.startsWith("video/") && !post.contentType.startsWith("audio/") && (
                <p className="text-muted-foreground py-8">Unsupported file type for preview.</p>
              )}
            </>
          )}
          <p className="mt-4 text-gray-700 dark:text-gray-300">{post.content}</p>
        </CardContent>
        <CardFooter className="flex justify-between items-center p-6 border-t">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => (post.hasLiked ? handleUnlike() : handleLike())}
              disabled={!session?.user?.id}
            >
              {post.hasLiked ? "❤️" : "🤍"} {post.likeCount}
            </Button>
            <span className="text-sm text-muted-foreground">Transcoding: {post.transcodingStatus}</span>
            <span className="text-sm text-muted-foreground">Moderation: {post.moderationStatus}</span>
          </div>
        </CardFooter>
        {/* Comments Section */}
        <CommentsSection postId={post.id} />
      </Card>
    </div>
  );
}
