"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Heart,
  Sparkles,
  Users,
  TrendingUp,
  Play,
  ArrowRight,
  Star,
  Zap,
  Globe,
  Music,
  Camera,
  Palette,
  Brain,
  Shield,
  Smartphone
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

export default function HomePage() {
  const [currentFeature, setCurrentFeature] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
    const interval = setInterval(() => {
      setCurrentFeature(prev => (prev + 1) % 3)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const features = [
    {
      icon: Heart,
      title: "Mood-Based Discovery",
      description: "Find content that matches your vibe with AI-powered mood detection",
      gradient: "from-pink-500 to-rose-500"
    },
    {
      icon: Users,
      title: "Creator Communities",
      description: "Connect with like-minded creators and build your fanbase",
      gradient: "from-purple-500 to-indigo-500"
    },
    {
      icon: Sparkles,
      title: "AI Content Studio",
      description: "Create amazing content with our AI-powered creation tools",
      gradient: "from-blue-500 to-cyan-500"
    }
  ]

  const stats = [
    { label: "Active Creators", value: "50K+", icon: Users },
    { label: "Content Pieces", value: "2M+", icon: Play },
    { label: "Daily Interactions", value: "10M+", icon: Heart },
    { label: "Countries", value: "150+", icon: Globe },
  ]

  const contentTypes = [
    { icon: Music, label: "Music & Audio", color: "text-green-500" },
    { icon: Camera, label: "Photography", color: "text-blue-500" },
    { icon: Palette, label: "Digital Art", color: "text-purple-500" },
    { icon: Play, label: "Video Content", color: "text-red-500" },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-900">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <Heart className="h-4 w-4 text-white" />
              </div>
              <span className="font-bold text-xl">HVPPY Central</span>
            </div>

            <nav className="hidden md:flex items-center gap-6">
              <Link href="/discover" className="text-sm font-medium hover:text-purple-600 transition-colors">
                Discover
              </Link>
              <Link href="/creators" className="text-sm font-medium hover:text-purple-600 transition-colors">
                Creators
              </Link>
              <Link href="/studio" className="text-sm font-medium hover:text-purple-600 transition-colors">
                Studio
              </Link>
              <Link href="/about" className="text-sm font-medium hover:text-purple-600 transition-colors">
                About
              </Link>
            </nav>

            <div className="flex items-center gap-3">
              <Button variant="ghost" asChild>
                <Link href="/auth/signin">Sign In</Link>
              </Button>
              <Button asChild className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                <Link href="/auth/signup">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 md:py-32">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : -50 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <Badge variant="outline" className="bg-purple-100 text-purple-700 border-purple-200">
                  <Sparkles className="h-3 w-3 mr-1" />
                  AI-Powered Social Platform
                </Badge>
                <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                  Where{' '}
                  <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Creators
                  </span>{' '}
                  and{' '}
                  <span className="bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent">
                    Fans
                  </span>{' '}
                  Connect
                </h1>
                <p className="text-xl text-muted-foreground leading-relaxed">
                  Discover content that matches your mood, connect with amazing creators,
                  and build your community on the most innovative social platform.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" asChild className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                  <Link href="/social">
                    Start Exploring
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/studio">
                    Create Content
                    <Sparkles className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>

              <div className="flex items-center gap-8">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
                    transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-2xl font-bold text-purple-600">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : 50 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative w-full max-w-md mx-auto">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl blur-3xl opacity-20 animate-pulse" />
                <Card className="relative bg-white/80 backdrop-blur-sm border-0 shadow-2xl">
                  <CardContent className="p-8">
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={currentFeature}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.5 }}
                        className="text-center space-y-4"
                      >
                        <div className={`mx-auto w-16 h-16 bg-gradient-to-r ${features[currentFeature].gradient} rounded-full flex items-center justify-center`}>
                          {React.createElement(features[currentFeature].icon, { className: "h-8 w-8 text-white" })}
                        </div>
                        <h3 className="text-xl font-semibold">{features[currentFeature].title}</h3>
                        <p className="text-muted-foreground">{features[currentFeature].description}</p>
                      </motion.div>
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white/50 dark:bg-gray-800/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to{' '}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Create & Connect
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From AI-powered content creation to mood-based discovery,
              HVPPY Central has all the tools you need.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Brain,
                title: "AI Content Studio",
                description: "Create amazing content with AI assistance",
                href: "/studio"
              },
              {
                icon: Heart,
                title: "Mood Discovery",
                description: "Find content that matches your vibe",
                href: "/discover"
              },
              {
                icon: Users,
                title: "Creator Network",
                description: "Connect with your community",
                href: "/creators"
              },
              {
                icon: TrendingUp,
                title: "Analytics Dashboard",
                description: "Track your growth and engagement",
                href: "/analytics"
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                      {React.createElement(feature.icon, { className: "h-6 w-6 text-white" })}
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Content Types Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Support for Every{' '}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Content Type
              </span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Whether you're a musician, artist, photographer, or video creator
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {contentTypes.map((type, index) => (
              <motion.div
                key={type.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 rounded-lg bg-white/50 dark:bg-gray-800/50 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors"
              >
                {React.createElement(type.icon, { className: `h-12 w-12 mx-auto mb-4 ${type.color}` })}
                <h3 className="font-semibold">{type.label}</h3>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-500 to-pink-500">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto space-y-8"
          >
            <h2 className="text-3xl md:text-5xl font-bold text-white">
              Ready to Join the Future of Social Media?
            </h2>
            <p className="text-xl text-white/90">
              Start creating, discovering, and connecting with HVPPY Central today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/auth/signup">
                  Get Started Free
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600" asChild>
                <Link href="/social-enhanced">
                  Try Demo
                  <Play className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <Heart className="h-4 w-4 text-white" />
                </div>
                <span className="font-bold text-xl">HVPPY Central</span>
              </div>
              <p className="text-gray-400">
                The ultimate social platform for creators and fans.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Platform</h3>
              <div className="space-y-2">
                <Link href="/discover" className="block text-gray-400 hover:text-white transition-colors">Discover</Link>
                <Link href="/creators" className="block text-gray-400 hover:text-white transition-colors">Creators</Link>
                <Link href="/studio" className="block text-gray-400 hover:text-white transition-colors">Studio</Link>
                <Link href="/analytics" className="block text-gray-400 hover:text-white transition-colors">Analytics</Link>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-400 hover:text-white transition-colors">About</Link>
                <Link href="/careers" className="block text-gray-400 hover:text-white transition-colors">Careers</Link>
                <Link href="/press" className="block text-gray-400 hover:text-white transition-colors">Press</Link>
                <Link href="/contact" className="block text-gray-400 hover:text-white transition-colors">Contact</Link>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <div className="space-y-2">
                <Link href="/help" className="block text-gray-400 hover:text-white transition-colors">Help Center</Link>
                <Link href="/privacy" className="block text-gray-400 hover:text-white transition-colors">Privacy</Link>
                <Link href="/terms" className="block text-gray-400 hover:text-white transition-colors">Terms</Link>
                <Link href="/safety" className="block text-gray-400 hover:text-white transition-colors">Safety</Link>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 HVPPY Central. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
