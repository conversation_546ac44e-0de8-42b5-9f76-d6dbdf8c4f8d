"use client"

import React, { useState, useEffect } from 'react'
import { SocialLayout } from '@/components/layout/social-layout'
import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { MoodSelector } from '@/components/discovery/mood-selector'
import { UserSuggestions } from '@/components/social/user-suggestions'
import { Notifications } from '@/components/social/notifications'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Home, 
  TrendingUp, 
  Users, 
  Heart,
  Compass,
  Plus,
  Filter,
  Settings,
  Sparkles
} from 'lucide-react'
import { FeedType } from '@/types/feed'
import { motion } from 'framer-motion'

export default function SocialMainPage() {
  const [activeTab, setActiveTab] = useState('home')
  const [selectedMoods, setSelectedMoods] = useState<string[]>(['happy'])
  const [showMoodSelector, setShowMoodSelector] = useState(false)

  // Check if user is new and show onboarding
  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem('hvppy_onboarding_data')
    if (!hasCompletedOnboarding) {
      // Redirect to onboarding for new users
      window.location.href = '/onboarding'
    }
  }, [])

  const feedTabs = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      feedType: FeedType.HOME,
      description: 'Your personalized feed'
    },
    {
      id: 'discover',
      label: 'Discover',
      icon: Compass,
      feedType: FeedType.DISCOVER,
      description: 'Explore new content'
    },
    {
      id: 'trending',
      label: 'Trending',
      icon: TrendingUp,
      feedType: FeedType.TRENDING,
      description: 'What\'s hot right now'
    },
    {
      id: 'following',
      label: 'Following',
      icon: Users,
      feedType: FeedType.FOLLOWING,
      description: 'From people you follow'
    },
  ]

  const currentFeedType = feedTabs.find(tab => tab.id === activeTab)?.feedType || FeedType.HOME

  return (
    <SocialLayout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 xl:grid-cols-5 gap-6 h-screen">
            {/* Left Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="xl:col-span-1 space-y-6 overflow-y-auto"
            >
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-500" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                    <Plus className="h-4 w-4" />
                    Create Post
                  </Button>
                  <Button variant="outline" className="w-full gap-2">
                    <Heart className="h-4 w-4" />
                    Mood Check
                  </Button>
                </CardContent>
              </Card>

              {/* Mood Selector */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-pink-500" />
                      Your Mood
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowMoodSelector(!showMoodSelector)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <MoodSelector
                    selectedMoods={selectedMoods}
                    onMoodChange={setSelectedMoods}
                    variant="compact"
                    maxSelections={3}
                    showIntensity={false}
                  />
                </CardContent>
              </Card>

              {/* User Suggestions */}
              <UserSuggestions
                title="Suggested for You"
                limit={5}
                variant="list"
                showStats={false}
              />
            </motion.div>

            {/* Main Feed */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="xl:col-span-3"
            >
              {/* Feed Header */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h1 className="text-2xl font-bold">Your Feed</h1>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="gap-2">
                      <Filter className="h-4 w-4" />
                      Filter
                    </Button>
                  </div>
                </div>

                {/* Feed Tabs */}
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4">
                    {feedTabs.map((tab) => {
                      const Icon = tab.icon
                      return (
                        <TabsTrigger
                          key={tab.id}
                          value={tab.id}
                          className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white"
                        >
                          <Icon className="h-4 w-4" />
                          <span className="hidden sm:inline">{tab.label}</span>
                        </TabsTrigger>
                      )
                    })}
                  </TabsList>
                </Tabs>
              </div>

              {/* Feed Content */}
              <div className="flex-1 rounded-lg overflow-hidden">
                <Tabs value={activeTab} className="w-full h-full">
                  {feedTabs.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id} className="mt-0 h-full">
                      <div className="h-[calc(100vh-200px)] w-full">
                        <VerticalFeedContainer
                          feedType={tab.feedType}
                          showControls={true}
                          enableSwipeGestures={true}
                          preloadDistance={3}
                          offloadDistance={10}
                          moodFilters={selectedMoods}
                          className="h-full w-full"
                        />
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            </motion.div>

            {/* Right Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="xl:col-span-1 space-y-6 overflow-y-auto"
            >
              {/* Notifications */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <Notifications
                    variant="compact"
                    maxHeight="300px"
                    showHeader={false}
                  />
                </CardContent>
              </Card>

              {/* Trending Topics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-orange-500" />
                    Trending
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {[
                    { tag: '#SummerVibes', posts: '12.5K' },
                    { tag: '#CreativeFlow', posts: '8.9K' },
                    { tag: '#MoodBoost', posts: '6.2K' },
                    { tag: '#ArtChallenge', posts: '4.8K' },
                    { tag: '#MusicMonday', posts: '3.1K' },
                  ].map((trend, index) => (
                    <motion.div
                      key={trend.tag}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                    >
                      <div>
                        <p className="font-medium text-sm">{trend.tag}</p>
                        <p className="text-xs text-muted-foreground">{trend.posts} posts</p>
                      </div>
                      <TrendingUp className="h-4 w-4 text-orange-500" />
                    </motion.div>
                  ))}
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Your Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-lg font-bold text-purple-600">156</div>
                      <div className="text-xs text-muted-foreground">Posts</div>
                    </div>
                    <div className="text-center p-3 bg-pink-50 dark:bg-pink-900/20 rounded-lg">
                      <div className="text-lg font-bold text-pink-600">2.1K</div>
                      <div className="text-xs text-muted-foreground">Followers</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">892</div>
                      <div className="text-xs text-muted-foreground">Following</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-lg font-bold text-green-600">45K</div>
                      <div className="text-xs text-muted-foreground">Likes</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Mood Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="h-5 w-5 text-pink-500" />
                    Mood Insights
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Happy</span>
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-700">
                        85%
                      </Badge>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '85%' }} />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Creative</span>
                      <Badge variant="outline" className="bg-purple-100 text-purple-700">
                        72%
                      </Badge>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-500 h-2 rounded-full" style={{ width: '72%' }} />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Energetic</span>
                      <Badge variant="outline" className="bg-orange-100 text-orange-700">
                        68%
                      </Badge>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-500 h-2 rounded-full" style={{ width: '68%' }} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </SocialLayout>
  )
}
