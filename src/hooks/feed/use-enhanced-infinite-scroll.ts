"use client"

import { useCallback, useEffect, useRef, useState } from 'react'
import { useFeedStore } from '@/lib/stores/feed-store'

interface UseEnhancedInfiniteScrollOptions {
  threshold?: number
  rootMargin?: string
  enabled?: boolean
  onItemChange?: (index: number) => void
  preloadDistance?: number
  offloadDistance?: number
  enableVirtualization?: boolean
  itemHeight?: number
}

interface UseEnhancedInfiniteScrollReturn {
  containerRef: React.RefObject<HTMLDivElement>
  observeElement: (element: HTMLElement | null) => void
  unobserveElement: (element: HTMLElement) => void
  scrollToIndex: (index: number) => void
  getCurrentIndex: () => number
  isScrolling: boolean
  visibleItems: any[]
}

export function useEnhancedInfiniteScroll(
  onLoadMore: () => void,
  options: UseEnhancedInfiniteScrollOptions = {}
): UseEnhancedInfiniteScrollReturn {
  const {
    threshold = 0.6,
    rootMargin = '100px',
    enabled = true,
    onItemChange,
    preloadDistance = 3,
    offloadDistance = 10,
    enableVirtualization = true,
    itemHeight = window?.innerHeight || 800,
  } = options

  const containerRef = useRef<HTMLDivElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const itemObserverRef = useRef<IntersectionObserver | null>(null)
  const [isScrolling, setIsScrolling] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  // Zustand store integration
  const {
    items,
    updateVirtualScroll,
    calculateVisibleRange,
    getVirtualItems,
    preloadContent,
    offloadContent,
    updateMetrics,
  } = useFeedStore()

  const visibleItems = enableVirtualization ? getVirtualItems() : items

  // Enhanced scroll performance monitoring
  const performanceRef = useRef({
    lastFrameTime: 0,
    frameCount: 0,
    fps: 60,
  })

  const measurePerformance = useCallback(() => {
    const now = performance.now()
    const deltaTime = now - performanceRef.current.lastFrameTime
    
    if (deltaTime >= 1000) {
      const fps = (performanceRef.current.frameCount * 1000) / deltaTime
      performanceRef.current.fps = fps
      performanceRef.current.frameCount = 0
      performanceRef.current.lastFrameTime = now
      
      updateMetrics({ fps })
    } else {
      performanceRef.current.frameCount++
    }
    
    requestAnimationFrame(measurePerformance)
  }, [updateMetrics])

  // Enhanced scroll handler with virtual scrolling
  const handleScroll = useCallback(() => {
    if (!containerRef.current || !enabled) return

    const container = containerRef.current
    const { scrollTop, clientHeight } = container

    setIsScrolling(true)
    
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // Virtual scrolling calculations
    if (enableVirtualization) {
      calculateVisibleRange(scrollTop, clientHeight)
    }

    // Calculate current item index
    const newIndex = Math.round(scrollTop / itemHeight)
    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < items.length) {
      setCurrentIndex(newIndex)
      onItemChange?.(newIndex)
      
      // Trigger preloading and offloading
      preloadContent(items, newIndex, preloadDistance)
      offloadContent(newIndex, offloadDistance)
    }

    // Set scrolling to false after scroll ends
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
    }, 150)
  }, [
    enabled,
    enableVirtualization,
    itemHeight,
    currentIndex,
    items,
    onItemChange,
    preloadDistance,
    offloadDistance,
    calculateVisibleRange,
    preloadContent,
    offloadContent,
  ])

  // Intersection observer for infinite loading
  const observeElement = useCallback((element: HTMLElement | null) => {
    if (!element || !enabled) return

    if (observerRef.current) {
      observerRef.current.observe(element)
    }
  }, [enabled])

  const unobserveElement = useCallback((element: HTMLElement) => {
    if (observerRef.current) {
      observerRef.current.unobserve(element)
    }
  }, [])

  // Enhanced scroll to index with smooth animation
  const scrollToIndex = useCallback((index: number) => {
    if (!containerRef.current || index < 0 || index >= items.length) return

    const targetScrollTop = index * itemHeight
    
    containerRef.current.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    })

    setCurrentIndex(index)
    onItemChange?.(index)
  }, [items.length, itemHeight, onItemChange])

  const getCurrentIndex = useCallback(() => currentIndex, [currentIndex])

  // Initialize observers
  useEffect(() => {
    if (!enabled) return

    // Intersection observer for infinite loading
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            onLoadMore()
          }
        })
      },
      {
        threshold,
        rootMargin,
      }
    )

    // Item visibility observer for current index tracking
    itemObserverRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement
            const postId = element.dataset.postId
            const index = items.findIndex(item => item.post.id === postId)
            
            if (index !== -1 && index !== currentIndex) {
              setCurrentIndex(index)
              onItemChange?.(index)
            }
          }
        })
      },
      {
        threshold: 0.6,
        root: containerRef.current,
      }
    )

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
      if (itemObserverRef.current) {
        itemObserverRef.current.disconnect()
      }
    }
  }, [enabled, threshold, rootMargin, onLoadMore, items, currentIndex, onItemChange])

  // Attach scroll listener
  useEffect(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll, enabled])

  // Start performance monitoring
  useEffect(() => {
    if (enabled) {
      requestAnimationFrame(measurePerformance)
    }
  }, [enabled, measurePerformance])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  return {
    containerRef,
    observeElement,
    unobserveElement,
    scrollToIndex,
    getCurrentIndex,
    isScrolling,
    visibleItems,
  }
}

// Hook for virtual scrolling specifically
export function useVirtualScrolling(itemHeight: number = 800) {
  const {
    virtualScroll,
    updateVirtualScroll,
    calculateVisibleRange,
    getVirtualItems,
  } = useFeedStore()

  const updateScrollPosition = useCallback((scrollTop: number, containerHeight: number) => {
    calculateVisibleRange(scrollTop, containerHeight)
  }, [calculateVisibleRange])

  const setItemHeight = useCallback((height: number) => {
    updateVirtualScroll({ itemHeight: height })
  }, [updateVirtualScroll])

  const setOverscan = useCallback((overscan: number) => {
    updateVirtualScroll({ overscan })
  }, [updateVirtualScroll])

  return {
    virtualScroll,
    visibleItems: getVirtualItems(),
    updateScrollPosition,
    setItemHeight,
    setOverscan,
  }
}

// Hook for scroll performance monitoring
export function useScrollPerformance() {
  const { metrics, updateMetrics } = useFeedStore()
  const [performanceData, setPerformanceData] = useState({
    fps: 60,
    frameTime: 0,
    scrollEvents: 0,
    lastMeasurement: Date.now(),
  })

  const measureScrollPerformance = useCallback(() => {
    const now = performance.now()
    const frameTime = now - performanceData.lastMeasurement
    
    setPerformanceData(prev => ({
      ...prev,
      frameTime,
      scrollEvents: prev.scrollEvents + 1,
      lastMeasurement: now,
    }))

    // Update store metrics
    updateMetrics({
      scrollPerformance: frameTime < 16.67 ? 100 : Math.max(0, 100 - (frameTime - 16.67) * 2)
    })
  }, [performanceData.lastMeasurement, updateMetrics])

  return {
    performanceData,
    metrics,
    measureScrollPerformance,
  }
}
