"use client"

import { useCallback, useEffect, useRef, useState } from 'react'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { useEnhancedInteractionStore } from '@/lib/stores/enhanced-interaction-store'
import { useFeedStore } from '@/lib/stores/feed-store'

interface MediaElement {
  id: string
  element: HTMLVideoElement | HTMLAudioElement
  type: 'video' | 'audio'
  postId: string
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  muted: boolean
}

interface UseEnhancedMediaAutoplayOptions {
  threshold?: number
  autoplayDelay?: number
  pauseDelay?: number
  enableVideoAutoplay?: boolean
  enableAudioAutoplay?: boolean
  muteByDefault?: boolean
  preloadDistance?: number
  maxConcurrentPlayers?: number
}

interface UseEnhancedMediaAutoplayReturn {
  registerMediaElement: (element: HTMLVideoElement | HTMLAudioElement, postId: string) => void
  unregisterMediaElement: (elementId: string) => void
  playMedia: (elementId: string) => Promise<void>
  pauseMedia: (elementId: string) => void
  setVolume: (elementId: string, volume: number) => void
  setMuted: (elementId: string, muted: boolean) => void
  getCurrentlyPlaying: () => MediaElement[]
  getMediaElement: (elementId: string) => MediaElement | null
  isMediaPlaying: (elementId: string) => boolean
}

export function useEnhancedMediaAutoplay(
  options: UseEnhancedMediaAutoplayOptions = {}
): UseEnhancedMediaAutoplayReturn {
  const {
    threshold = 0.6,
    autoplayDelay = 200,
    pauseDelay = 300,
    enableVideoAutoplay = true,
    enableAudioAutoplay = false,
    muteByDefault = true,
    preloadDistance = 2,
    maxConcurrentPlayers = 1,
  } = options

  const mediaElementsRef = useRef<Map<string, MediaElement>>(new Map())
  const observerRef = useRef<IntersectionObserver | null>(null)
  const autoplayTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Set<string>>(new Set())

  // Store integration
  const { globalVolume, globalMuted, setGlobalVolume, setGlobalMuted } = usePlayerStore()
  const { recordView } = useEnhancedInteractionStore()
  const { currentIndex, items, preferences } = useFeedStore()

  // Enhanced media registration with metadata
  const registerMediaElement = useCallback((
    element: HTMLVideoElement | HTMLAudioElement,
    postId: string
  ) => {
    const elementId = `${postId}-${element.tagName.toLowerCase()}-${Date.now()}`
    
    const mediaElement: MediaElement = {
      id: elementId,
      element,
      type: element.tagName.toLowerCase() as 'video' | 'audio',
      postId,
      isPlaying: false,
      currentTime: 0,
      duration: element.duration || 0,
      volume: element.volume,
      muted: element.muted,
    }

    // Apply global settings
    element.muted = muteByDefault || globalMuted
    element.volume = globalVolume
    element.preload = 'metadata'

    // Add event listeners for enhanced tracking
    const handleLoadedMetadata = () => {
      mediaElement.duration = element.duration
      mediaElementsRef.current.set(elementId, { ...mediaElement })
    }

    const handleTimeUpdate = () => {
      mediaElement.currentTime = element.currentTime
      mediaElementsRef.current.set(elementId, { ...mediaElement })
    }

    const handlePlay = () => {
      mediaElement.isPlaying = true
      setCurrentlyPlaying(prev => new Set([...prev, elementId]))
      mediaElementsRef.current.set(elementId, { ...mediaElement })
    }

    const handlePause = () => {
      mediaElement.isPlaying = false
      setCurrentlyPlaying(prev => {
        const newSet = new Set(prev)
        newSet.delete(elementId)
        return newSet
      })
      mediaElementsRef.current.set(elementId, { ...mediaElement })
    }

    const handleEnded = () => {
      mediaElement.isPlaying = false
      setCurrentlyPlaying(prev => {
        const newSet = new Set(prev)
        newSet.delete(elementId)
        return newSet
      })
      
      // Record view completion
      recordView(postId, element.currentTime * 1000)
      
      mediaElementsRef.current.set(elementId, { ...mediaElement })
    }

    const handleVolumeChange = () => {
      mediaElement.volume = element.volume
      mediaElement.muted = element.muted
      mediaElementsRef.current.set(elementId, { ...mediaElement })
    }

    element.addEventListener('loadedmetadata', handleLoadedMetadata)
    element.addEventListener('timeupdate', handleTimeUpdate)
    element.addEventListener('play', handlePlay)
    element.addEventListener('pause', handlePause)
    element.addEventListener('ended', handleEnded)
    element.addEventListener('volumechange', handleVolumeChange)

    // Store cleanup function
    mediaElement.element.dataset.cleanup = JSON.stringify({
      elementId,
      listeners: ['loadedmetadata', 'timeupdate', 'play', 'pause', 'ended', 'volumechange']
    })

    mediaElementsRef.current.set(elementId, mediaElement)

    // Start observing for autoplay
    if (observerRef.current) {
      observerRef.current.observe(element)
    }

    return elementId
  }, [globalVolume, globalMuted, muteByDefault, recordView])

  // Enhanced media unregistration with cleanup
  const unregisterMediaElement = useCallback((elementId: string) => {
    const mediaElement = mediaElementsRef.current.get(elementId)
    if (!mediaElement) return

    // Clear any pending autoplay timeouts
    const timeout = autoplayTimeoutsRef.current.get(elementId)
    if (timeout) {
      clearTimeout(timeout)
      autoplayTimeoutsRef.current.delete(elementId)
    }

    // Stop observing
    if (observerRef.current) {
      observerRef.current.unobserve(mediaElement.element)
    }

    // Remove event listeners
    const cleanupData = mediaElement.element.dataset.cleanup
    if (cleanupData) {
      try {
        const { listeners } = JSON.parse(cleanupData)
        listeners.forEach((event: string) => {
          mediaElement.element.removeEventListener(event, () => {})
        })
      } catch (error) {
        console.warn('Failed to parse cleanup data:', error)
      }
    }

    // Pause if playing
    if (mediaElement.isPlaying) {
      mediaElement.element.pause()
    }

    // Remove from tracking
    mediaElementsRef.current.delete(elementId)
    setCurrentlyPlaying(prev => {
      const newSet = new Set(prev)
      newSet.delete(elementId)
      return newSet
    })
  }, [])

  // Enhanced play function with concurrency control
  const playMedia = useCallback(async (elementId: string): Promise<void> => {
    const mediaElement = mediaElementsRef.current.get(elementId)
    if (!mediaElement) return

    const { element, type, postId } = mediaElement

    // Check if autoplay is enabled for this media type
    if (type === 'video' && !enableVideoAutoplay) return
    if (type === 'audio' && !enableAudioAutoplay) return

    // Enforce concurrent player limit
    if (currentlyPlaying.size >= maxConcurrentPlayers) {
      // Pause other players
      const playingElements = Array.from(currentlyPlaying)
      for (const playingId of playingElements) {
        if (playingId !== elementId) {
          pauseMedia(playingId)
        }
      }
    }

    try {
      // Apply current global settings
      element.muted = globalMuted
      element.volume = globalVolume

      await element.play()
      
      // Record view start
      recordView(postId, 0)
    } catch (error) {
      console.warn(`Failed to play media ${elementId}:`, error)
      
      // Handle autoplay policy restrictions
      if (error instanceof DOMException && error.name === 'NotAllowedError') {
        // Mute and try again
        element.muted = true
        try {
          await element.play()
        } catch (retryError) {
          console.warn(`Failed to play muted media ${elementId}:`, retryError)
        }
      }
    }
  }, [
    enableVideoAutoplay,
    enableAudioAutoplay,
    maxConcurrentPlayers,
    currentlyPlaying,
    globalMuted,
    globalVolume,
    recordView,
  ])

  // Enhanced pause function
  const pauseMedia = useCallback((elementId: string) => {
    const mediaElement = mediaElementsRef.current.get(elementId)
    if (!mediaElement) return

    const { element, postId } = mediaElement

    // Record view duration before pausing
    if (element.currentTime > 0) {
      recordView(postId, element.currentTime * 1000)
    }

    element.pause()
  }, [recordView])

  // Volume control
  const setVolume = useCallback((elementId: string, volume: number) => {
    const mediaElement = mediaElementsRef.current.get(elementId)
    if (!mediaElement) return

    mediaElement.element.volume = Math.max(0, Math.min(1, volume))
  }, [])

  // Mute control
  const setMuted = useCallback((elementId: string, muted: boolean) => {
    const mediaElement = mediaElementsRef.current.get(elementId)
    if (!mediaElement) return

    mediaElement.element.muted = muted
  }, [])

  // Utility functions
  const getCurrentlyPlaying = useCallback((): MediaElement[] => {
    return Array.from(currentlyPlaying)
      .map(id => mediaElementsRef.current.get(id))
      .filter(Boolean) as MediaElement[]
  }, [currentlyPlaying])

  const getMediaElement = useCallback((elementId: string): MediaElement | null => {
    return mediaElementsRef.current.get(elementId) || null
  }, [])

  const isMediaPlaying = useCallback((elementId: string): boolean => {
    return currentlyPlaying.has(elementId)
  }, [currentlyPlaying])

  // Initialize intersection observer for autoplay
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const element = entry.target as HTMLVideoElement | HTMLAudioElement
          const elementId = Array.from(mediaElementsRef.current.entries())
            .find(([_, media]) => media.element === element)?.[0]

          if (!elementId) return

          if (entry.isIntersecting) {
            // Schedule autoplay
            const timeout = setTimeout(() => {
              playMedia(elementId)
            }, autoplayDelay)
            
            autoplayTimeoutsRef.current.set(elementId, timeout)
          } else {
            // Cancel scheduled autoplay or pause if playing
            const timeout = autoplayTimeoutsRef.current.get(elementId)
            if (timeout) {
              clearTimeout(timeout)
              autoplayTimeoutsRef.current.delete(elementId)
            }

            // Schedule pause
            setTimeout(() => {
              pauseMedia(elementId)
            }, pauseDelay)
          }
        })
      },
      {
        threshold,
        rootMargin: '50px',
      }
    )

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [threshold, autoplayDelay, pauseDelay, playMedia, pauseMedia])

  // Global volume/mute sync
  useEffect(() => {
    mediaElementsRef.current.forEach((mediaElement) => {
      mediaElement.element.volume = globalVolume
      mediaElement.element.muted = globalMuted
    })
  }, [globalVolume, globalMuted])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timeouts
      autoplayTimeoutsRef.current.forEach(timeout => clearTimeout(timeout))
      autoplayTimeoutsRef.current.clear()

      // Pause all media
      mediaElementsRef.current.forEach((mediaElement) => {
        mediaElement.element.pause()
      })

      // Disconnect observer
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  return {
    registerMediaElement,
    unregisterMediaElement,
    playMedia,
    pauseMedia,
    setVolume,
    setMuted,
    getCurrentlyPlaying,
    getMediaElement,
    isMediaPlaying,
  }
}
