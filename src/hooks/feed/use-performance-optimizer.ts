"use client"

import { useCallback, useEffect, useRef, useState } from 'react'
import { useFeedStore } from '@/lib/stores/feed-store'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  renderTime: number
  scrollPerformance: number
  networkLatency: number
}

interface UsePerformanceOptimizerOptions {
  targetFPS?: number
  memoryThreshold?: number
  enableMetrics?: boolean
  enableOptimizations?: boolean
}

interface UsePerformanceOptimizerReturn {
  metrics: PerformanceMetrics
  isOptimized: boolean
  optimizationLevel: 'low' | 'medium' | 'high'
  enableOptimization: (level: 'low' | 'medium' | 'high') => void
  disableOptimization: () => void
  getRecommendations: () => string[]
}

export function usePerformanceOptimizer({
  targetFPS = 60,
  memoryThreshold = 100, // MB
  enableMetrics = true,
  enableOptimizations = true,
}: UsePerformanceOptimizerOptions = {}): UsePerformanceOptimizerReturn {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    renderTime: 0,
    scrollPerformance: 100,
    networkLatency: 0,
  })
  
  const [isOptimized, setIsOptimized] = useState(false)
  const [optimizationLevel, setOptimizationLevel] = useState<'low' | 'medium' | 'high'>('medium')
  
  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const renderTimesRef = useRef<number[]>([])
  const scrollTimesRef = useRef<number[]>([])
  const networkTimesRef = useRef<number[]>([])
  
  const { preferences, setPreferences } = useFeedStore()

  // FPS Monitoring
  const measureFPS = useCallback(() => {
    const now = performance.now()
    frameCountRef.current++
    
    if (now - lastTimeRef.current >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current))
      
      setMetrics(prev => ({ ...prev, fps }))
      
      frameCountRef.current = 0
      lastTimeRef.current = now
    }
    
    if (enableMetrics) {
      requestAnimationFrame(measureFPS)
    }
  }, [enableMetrics])

  // Memory Usage Monitoring
  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usedMB = memory.usedJSHeapSize / 1024 / 1024
      
      setMetrics(prev => ({ ...prev, memoryUsage: usedMB }))
    }
  }, [])

  // Render Time Monitoring
  const measureRenderTime = useCallback((startTime: number) => {
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    renderTimesRef.current.push(renderTime)
    if (renderTimesRef.current.length > 10) {
      renderTimesRef.current.shift()
    }
    
    const avgRenderTime = renderTimesRef.current.reduce((a, b) => a + b, 0) / renderTimesRef.current.length
    setMetrics(prev => ({ ...prev, renderTime: avgRenderTime }))
  }, [])

  // Scroll Performance Monitoring
  const measureScrollPerformance = useCallback(() => {
    let scrollStartTime = 0
    let isScrolling = false
    
    const handleScrollStart = () => {
      if (!isScrolling) {
        scrollStartTime = performance.now()
        isScrolling = true
      }
    }
    
    const handleScrollEnd = () => {
      if (isScrolling) {
        const scrollTime = performance.now() - scrollStartTime
        scrollTimesRef.current.push(scrollTime)
        
        if (scrollTimesRef.current.length > 10) {
          scrollTimesRef.current.shift()
        }
        
        const avgScrollTime = scrollTimesRef.current.reduce((a, b) => a + b, 0) / scrollTimesRef.current.length
        const scrollPerformance = Math.max(0, 100 - avgScrollTime)
        
        setMetrics(prev => ({ ...prev, scrollPerformance }))
        isScrolling = false
      }
    }
    
    let scrollTimeout: NodeJS.Timeout
    const handleScroll = () => {
      handleScrollStart()
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(handleScrollEnd, 150)
    }
    
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener('scroll', handleScroll)
      clearTimeout(scrollTimeout)
    }
  }, [])

  // Network Latency Monitoring
  const measureNetworkLatency = useCallback(async () => {
    try {
      const startTime = performance.now()
      await fetch('/api/health', { method: 'HEAD' })
      const latency = performance.now() - startTime
      
      networkTimesRef.current.push(latency)
      if (networkTimesRef.current.length > 5) {
        networkTimesRef.current.shift()
      }
      
      const avgLatency = networkTimesRef.current.reduce((a, b) => a + b, 0) / networkTimesRef.current.length
      setMetrics(prev => ({ ...prev, networkLatency: avgLatency }))
    } catch (error) {
      console.warn('Network latency measurement failed:', error)
    }
  }, [])

  // Optimization Strategies
  const applyOptimizations = useCallback((level: 'low' | 'medium' | 'high') => {
    const optimizations = {
      low: {
        preloadDistance: 2,
        offloadDistance: 8,
        preferredQuality: 'medium' as const,
        enableHapticFeedback: true,
        autoPlay: true,
      },
      medium: {
        preloadDistance: 3,
        offloadDistance: 10,
        preferredQuality: 'auto' as const,
        enableHapticFeedback: true,
        autoPlay: true,
      },
      high: {
        preloadDistance: 5,
        offloadDistance: 15,
        preferredQuality: 'high' as const,
        enableHapticFeedback: true,
        autoPlay: true,
      },
    }
    
    const config = optimizations[level]
    setPreferences(config)
    setOptimizationLevel(level)
    setIsOptimized(true)
  }, [setPreferences])

  // Auto-optimization based on performance metrics
  const autoOptimize = useCallback(() => {
    if (!enableOptimizations) return
    
    const { fps, memoryUsage, renderTime, scrollPerformance } = metrics
    
    // Determine optimization level based on performance
    if (fps < 30 || memoryUsage > memoryThreshold * 1.5 || renderTime > 50 || scrollPerformance < 50) {
      applyOptimizations('low')
    } else if (fps < 45 || memoryUsage > memoryThreshold || renderTime > 30 || scrollPerformance < 70) {
      applyOptimizations('medium')
    } else if (fps >= targetFPS && memoryUsage < memoryThreshold * 0.7 && renderTime < 20 && scrollPerformance > 80) {
      applyOptimizations('high')
    }
  }, [metrics, memoryThreshold, targetFPS, enableOptimizations, applyOptimizations])

  // Performance Recommendations
  const getRecommendations = useCallback((): string[] => {
    const recommendations: string[] = []
    const { fps, memoryUsage, renderTime, scrollPerformance, networkLatency } = metrics
    
    if (fps < targetFPS) {
      recommendations.push('Consider reducing video quality or disabling auto-play')
    }
    
    if (memoryUsage > memoryThreshold) {
      recommendations.push('Clear cache or reduce preload distance')
    }
    
    if (renderTime > 30) {
      recommendations.push('Optimize component rendering or reduce visual effects')
    }
    
    if (scrollPerformance < 70) {
      recommendations.push('Enable hardware acceleration or reduce scroll animations')
    }
    
    if (networkLatency > 500) {
      recommendations.push('Check network connection or enable offline mode')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Performance is optimal!')
    }
    
    return recommendations
  }, [metrics, targetFPS, memoryThreshold])

  // Manual optimization controls
  const enableOptimization = useCallback((level: 'low' | 'medium' | 'high') => {
    applyOptimizations(level)
  }, [applyOptimizations])

  const disableOptimization = useCallback(() => {
    setIsOptimized(false)
    // Reset to default preferences
    setPreferences({
      preloadDistance: 3,
      offloadDistance: 10,
      preferredQuality: 'auto',
      enableHapticFeedback: true,
      autoPlay: true,
    })
  }, [setPreferences])

  // Initialize monitoring
  useEffect(() => {
    if (!enableMetrics) return
    
    // Start FPS monitoring
    requestAnimationFrame(measureFPS)
    
    // Start memory monitoring
    const memoryInterval = setInterval(measureMemoryUsage, 2000)
    
    // Start scroll performance monitoring
    const cleanupScroll = measureScrollPerformance()
    
    // Start network monitoring
    const networkInterval = setInterval(measureNetworkLatency, 10000)
    
    return () => {
      clearInterval(memoryInterval)
      clearInterval(networkInterval)
      cleanupScroll()
    }
  }, [enableMetrics, measureFPS, measureMemoryUsage, measureScrollPerformance, measureNetworkLatency])

  // Auto-optimization effect
  useEffect(() => {
    if (enableOptimizations) {
      const optimizationInterval = setInterval(autoOptimize, 5000)
      return () => clearInterval(optimizationInterval)
    }
  }, [autoOptimize, enableOptimizations])

  return {
    metrics,
    isOptimized,
    optimizationLevel,
    enableOptimization,
    disableOptimization,
    getRecommendations,
  }
}
