"use client"

import { useCallback } from 'react'
import { ReactionType } from '@/types'
import { toast } from 'sonner'
import {
  useInteractionStore,
  selectUserReaction,
  selectHasMemory,
  selectIsReactionLoading,
  selectIsMemoryLoading,
  selectIsShareLoading,
  selectIsSaveLoading,
} from '@/lib/stores/interaction-store'
import { useFeedStore } from '@/lib/stores/feed-store'

interface UseContentInteractionsReturn {
  // Core interactions
  react: (postId: string, type: ReactionType, mood?: string) => Promise<void>
  unreact: (postId: string) => Promise<void>
  toggleMemory: (postId: string) => Promise<void>
  toggleSave: (postId: string) => Promise<void>
  share: (postId: string) => Promise<void>
  comment: (postId: string, content: string) => Promise<void>

  // State getters
  getUserReaction: (postId: string) => ReactionType | undefined
  hasMemory: (postId: string) => boolean
  hasSave: (postId: string) => boolean
  isReactionLoading: (postId: string) => boolean
  isMemoryLoading: (postId: string) => boolean
  isShareLoading: (postId: string) => boolean
  isSaveLoading: (postId: string) => boolean
}

export function useContentInteractions(): UseContentInteractionsReturn {
  const {
    setReaction,
    removeReaction,
    addMemory,
    removeMemory,
    addSave,
    removeSave,
    addShare,
    setReactionLoading,
    setMemoryLoading,
    setShareLoading,
    setSaveLoading,
  } = useInteractionStore()

  const { updateItem } = useFeedStore()

  const react = useCallback(async (postId: string, type: ReactionType, mood?: string) => {
    // Optimistic update
    setReaction(postId, type)
    setReactionLoading(postId, true)

    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, mood }),
      })

      if (!response.ok) {
        // Revert optimistic update
        removeReaction(postId)
        throw new Error('Failed to react to post')
      }

      // Update feed item with new like count
      const data = await response.json()
      updateItem(postId, {
        post: {
          likeCount: data.likeCount,
          userHasLiked: true
        }
      })

      toast.success('Reaction added!')
    } catch (error) {
      console.error('Error reacting to post:', error)
      toast.error('Failed to react to post')
      throw error
    } finally {
      setReactionLoading(postId, false)
    }
  }, [setReaction, removeReaction, setReactionLoading, updateItem])

  const unreact = useCallback(async (postId: string) => {
    // Store current reaction for potential rollback
    const currentReaction = useInteractionStore.getState().reactions.get(postId)

    // Optimistic update
    removeReaction(postId)
    setReactionLoading(postId, true)

    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        // Revert optimistic update
        if (currentReaction) {
          setReaction(postId, currentReaction)
        }
        throw new Error('Failed to remove reaction')
      }

      // Update feed item with new like count
      const data = await response.json()
      updateItem(postId, {
        post: {
          likeCount: data.likeCount,
          userHasLiked: false
        }
      })

      toast.success('Reaction removed!')
    } catch (error) {
      console.error('Error removing reaction:', error)
      toast.error('Failed to remove reaction')
      throw error
    } finally {
      setReactionLoading(postId, false)
    }
  }, [removeReaction, setReaction, setReactionLoading, updateItem])

  const toggleMemory = useCallback(async (postId: string) => {
    const hasCurrentMemory = useInteractionStore.getState().memories.has(postId)

    // Optimistic update
    if (hasCurrentMemory) {
      removeMemory(postId)
    } else {
      addMemory(postId)
    }
    setMemoryLoading(postId, true)

    try {
      const response = await fetch(`/api/post/${postId}/memory`, {
        method: 'POST',
      })

      if (!response.ok) {
        // Revert optimistic update
        if (hasCurrentMemory) {
          addMemory(postId)
        } else {
          removeMemory(postId)
        }
        throw new Error('Failed to toggle memory')
      }

      const data = await response.json()
      toast.success(data.saved ? 'Saved to memories!' : 'Removed from memories!')
    } catch (error) {
      console.error('Error toggling memory:', error)
      toast.error('Failed to save memory')
      throw error
    } finally {
      setMemoryLoading(postId, false)
    }
  }, [addMemory, removeMemory, setMemoryLoading])

  const toggleSave = useCallback(async (postId: string) => {
    const hasCurrentSave = useInteractionStore.getState().saves.has(postId)

    // Optimistic update
    if (hasCurrentSave) {
      removeSave(postId)
    } else {
      addSave(postId)
    }
    setSaveLoading(postId, true)

    try {
      const response = await fetch(`/api/post/${postId}/save`, {
        method: hasCurrentSave ? 'DELETE' : 'POST',
      })

      if (!response.ok) {
        // Revert optimistic update
        if (hasCurrentSave) {
          addSave(postId)
        } else {
          removeSave(postId)
        }
        throw new Error('Failed to toggle save')
      }

      toast.success(hasCurrentSave ? 'Removed from saved!' : 'Saved for later!')
    } catch (error) {
      console.error('Error toggling save:', error)
      toast.error('Failed to save post')
      throw error
    } finally {
      setSaveLoading(postId, false)
    }
  }, [addSave, removeSave, setSaveLoading])

  const share = useCallback(async (postId: string) => {
    setShareLoading(postId, true)

    try {
      // Try native sharing first
      if (navigator.share) {
        await navigator.share({
          title: 'Check out this content on HVPPY',
          url: `${window.location.origin}/post/${postId}`,
        })
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(`${window.location.origin}/post/${postId}`)
        toast.success('Link copied to clipboard!')
      }

      // Track share and update store
      addShare(postId)
      await fetch(`/api/post/${postId}/share`, {
        method: 'POST',
      })
    } catch (error) {
      console.error('Error sharing post:', error)
      toast.error('Failed to share post')
      throw error
    } finally {
      setShareLoading(postId, false)
    }
  }, [addShare, setShareLoading])

  const comment = useCallback(async (postId: string, content: string) => {
    try {
      const response = await fetch(`/api/post/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content }),
      })

      if (!response.ok) {
        throw new Error('Failed to add comment')
      }

      // Update feed item with new comment count
      const data = await response.json()
      updateItem(postId, {
        post: {
          commentCount: data.commentCount
        }
      })

      toast.success('Comment added!')
    } catch (error) {
      console.error('Error adding comment:', error)
      toast.error('Failed to add comment')
      throw error
    }
  }, [updateItem])

  // State getters using selectors
  const getUserReaction = useCallback((postId: string) => {
    return useInteractionStore.getState().reactions.get(postId)
  }, [])

  const hasMemory = useCallback((postId: string) => {
    return useInteractionStore.getState().memories.has(postId)
  }, [])

  const hasSave = useCallback((postId: string) => {
    return useInteractionStore.getState().saves.has(postId)
  }, [])

  const isReactionLoading = useCallback((postId: string) => {
    return useInteractionStore.getState().loadingReactions.has(postId)
  }, [])

  const isMemoryLoading = useCallback((postId: string) => {
    return useInteractionStore.getState().loadingMemories.has(postId)
  }, [])

  const isShareLoading = useCallback((postId: string) => {
    return useInteractionStore.getState().loadingShares.has(postId)
  }, [])

  const isSaveLoading = useCallback((postId: string) => {
    return useInteractionStore.getState().loadingSaves.has(postId)
  }, [])

  return {
    react,
    unreact,
    toggleMemory,
    toggleSave,
    share,
    comment,
    getUserReaction,
    hasMemory,
    hasSave,
    isReactionLoading,
    isMemoryLoading,
    isShareLoading,
    isSaveLoading,
  }
}