"use client"

import { useCallback, useState, useEffect } from 'react'
import { useEnhancedInteractionStore } from '@/lib/stores/enhanced-interaction-store'
import { useFeedStore } from '@/lib/stores/feed-store'
import { InteractionType } from '@/types/feed'
import { toast } from 'sonner'

interface InteractionState {
  isLiked: boolean
  isShared: boolean
  isFollowing: boolean
  isSaved: boolean
  likeCount: number
  shareCount: number
  commentCount: number
  viewCount: number
}

interface UseEnhancedContentInteractionsOptions {
  enableOptimisticUpdates?: boolean
  enableHapticFeedback?: boolean
  enableAnalytics?: boolean
  debounceMs?: number
}

interface UseEnhancedContentInteractionsReturn {
  // Interaction states
  getInteractionState: (postId: string) => InteractionState
  
  // Core interactions
  toggleLike: (postId: string, mood?: string) => Promise<void>
  toggleFollow: (userId: string) => Promise<void>
  sharePost: (postId: string, platform?: string) => Promise<void>
  addComment: (postId: string, content: string, mood?: string) => Promise<void>
  savePost: (postId: string) => Promise<void>
  reportPost: (postId: string, reason: string) => Promise<void>
  
  // Advanced interactions
  reactWithMood: (postId: string, mood: string) => Promise<void>
  addToMemory: (postId: string) => Promise<void>
  createRemix: (postId: string) => Promise<void>
  
  // Bulk operations
  likeMultiplePosts: (postIds: string[]) => Promise<void>
  shareMultiplePosts: (postIds: string[], platform?: string) => Promise<void>
  
  // Analytics
  trackEngagement: (postId: string, type: string, duration?: number) => void
  getEngagementScore: (postId: string) => number
  
  // Real-time features
  subscribeToPostUpdates: (postId: string, callback: (update: any) => void) => () => void
  
  // Utilities
  isInteractionPending: (postId: string, type: InteractionType) => boolean
  getInteractionHistory: (userId: string) => any[]
  clearPendingInteractions: () => void
}

export function useEnhancedContentInteractions(
  options: UseEnhancedContentInteractionsOptions = {}
): UseEnhancedContentInteractionsReturn {
  const {
    enableOptimisticUpdates = true,
    enableHapticFeedback = true,
    enableAnalytics = true,
    debounceMs = 300,
  } = options

  const [pendingInteractions, setPendingInteractions] = useState<Set<string>>(new Set())
  const [interactionStates, setInteractionStates] = useState<Map<string, InteractionState>>(new Map())

  // Store integration
  const {
    toggleLike: storeLike,
    toggleFollow: storeFollow,
    sharePost: storeShare,
    addComment: storeComment,
    recordView,
    getPostInteractions,
    getEngagementScore: storeEngagementScore,
    following,
    syncInteractions,
  } = useEnhancedInteractionStore()

  const {
    items,
    updateItem,
    recordEngagement,
  } = useFeedStore()

  // Haptic feedback helper
  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!enableHapticFeedback || !navigator.vibrate) return
    
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30, 10, 30],
    }
    
    navigator.vibrate(patterns[type])
  }, [enableHapticFeedback])

  // Get current interaction state for a post
  const getInteractionState = useCallback((postId: string): InteractionState => {
    const cached = interactionStates.get(postId)
    if (cached) return cached

    const post = items.find(item => item.post.id === postId)?.post
    const interactions = getPostInteractions(postId)
    
    const likeInteraction = interactions.find(i => i.type === InteractionType.LIKE)
    const shareInteractions = interactions.filter(i => i.type === InteractionType.SHARE)
    
    const state: InteractionState = {
      isLiked: !!likeInteraction,
      isShared: shareInteractions.length > 0,
      isFollowing: post?.userId ? following.has(post.userId) : false,
      isSaved: false, // TODO: Implement saved posts
      likeCount: post?.likeCount || 0,
      shareCount: post?.shareCount || 0,
      commentCount: post?.comments?.length || 0,
      viewCount: post?.viewCount || 0,
    }

    setInteractionStates(prev => new Map(prev).set(postId, state))
    return state
  }, [items, getPostInteractions, following, interactionStates])

  // Enhanced like toggle with optimistic updates
  const toggleLike = useCallback(async (postId: string, mood?: string) => {
    const interactionId = `like-${postId}-${Date.now()}`
    
    if (pendingInteractions.has(interactionId)) return
    setPendingInteractions(prev => new Set(prev).add(interactionId))

    try {
      const currentState = getInteractionState(postId)
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        const newState = {
          ...currentState,
          isLiked: !currentState.isLiked,
          likeCount: currentState.isLiked 
            ? Math.max(0, currentState.likeCount - 1)
            : currentState.likeCount + 1
        }
        
        setInteractionStates(prev => new Map(prev).set(postId, newState))
        
        // Update feed item
        updateItem(postId, {
          post: {
            ...items.find(item => item.post.id === postId)?.post,
            likeCount: newState.likeCount
          }
        } as any)
      }

      // Haptic feedback
      triggerHapticFeedback(currentState.isLiked ? 'light' : 'medium')

      // Store interaction
      await storeLike(postId, mood)

      // Analytics
      if (enableAnalytics) {
        recordEngagement(postId, currentState.isLiked ? 'unlike' : 'like')
      }

      // Success feedback
      if (!currentState.isLiked) {
        toast.success('Liked! ❤️', { duration: 1000 })
      }

    } catch (error) {
      console.error('Failed to toggle like:', error)
      
      // Rollback optimistic update
      if (enableOptimisticUpdates) {
        const originalState = getInteractionState(postId)
        setInteractionStates(prev => new Map(prev).set(postId, originalState))
      }
      
      toast.error('Failed to update like')
    } finally {
      setPendingInteractions(prev => {
        const newSet = new Set(prev)
        newSet.delete(interactionId)
        return newSet
      })
    }
  }, [
    pendingInteractions,
    getInteractionState,
    enableOptimisticUpdates,
    enableAnalytics,
    triggerHapticFeedback,
    storeLike,
    updateItem,
    items,
    recordEngagement,
  ])

  // Enhanced follow toggle
  const toggleFollow = useCallback(async (userId: string) => {
    const interactionId = `follow-${userId}-${Date.now()}`
    
    if (pendingInteractions.has(interactionId)) return
    setPendingInteractions(prev => new Set(prev).add(interactionId))

    try {
      const isCurrentlyFollowing = following.has(userId)
      
      // Haptic feedback
      triggerHapticFeedback('medium')

      // Store interaction
      await storeFollow(userId)

      // Success feedback
      toast.success(isCurrentlyFollowing ? 'Unfollowed' : 'Following! 👥', { duration: 1500 })

    } catch (error) {
      console.error('Failed to toggle follow:', error)
      toast.error('Failed to update follow status')
    } finally {
      setPendingInteractions(prev => {
        const newSet = new Set(prev)
        newSet.delete(interactionId)
        return newSet
      })
    }
  }, [pendingInteractions, following, triggerHapticFeedback, storeFollow])

  // Enhanced share functionality
  const sharePost = useCallback(async (postId: string, platform?: string) => {
    const interactionId = `share-${postId}-${Date.now()}`
    
    if (pendingInteractions.has(interactionId)) return
    setPendingInteractions(prev => new Set(prev).add(interactionId))

    try {
      // Optimistic update
      if (enableOptimisticUpdates) {
        const currentState = getInteractionState(postId)
        const newState = {
          ...currentState,
          isShared: true,
          shareCount: currentState.shareCount + 1
        }
        setInteractionStates(prev => new Map(prev).set(postId, newState))
      }

      // Haptic feedback
      triggerHapticFeedback('heavy')

      // Store interaction
      await storeShare(postId, platform)

      // Analytics
      if (enableAnalytics) {
        recordEngagement(postId, 'share')
      }

      // Native sharing if available
      if (navigator.share && !platform) {
        const post = items.find(item => item.post.id === postId)?.post
        await navigator.share({
          title: post?.title || 'Check out this content!',
          text: post?.description || 'Amazing content from HVPPY Central',
          url: `${window.location.origin}/post/${postId}`,
        })
      }

      toast.success('Shared! 🚀', { duration: 1500 })

    } catch (error) {
      console.error('Failed to share post:', error)
      toast.error('Failed to share post')
    } finally {
      setPendingInteractions(prev => {
        const newSet = new Set(prev)
        newSet.delete(interactionId)
        return newSet
      })
    }
  }, [
    pendingInteractions,
    enableOptimisticUpdates,
    enableAnalytics,
    getInteractionState,
    triggerHapticFeedback,
    storeShare,
    recordEngagement,
    items,
  ])

  // Enhanced comment functionality
  const addComment = useCallback(async (postId: string, content: string, mood?: string) => {
    const interactionId = `comment-${postId}-${Date.now()}`
    
    if (pendingInteractions.has(interactionId)) return
    setPendingInteractions(prev => new Set(prev).add(interactionId))

    try {
      // Optimistic update
      if (enableOptimisticUpdates) {
        const currentState = getInteractionState(postId)
        const newState = {
          ...currentState,
          commentCount: currentState.commentCount + 1
        }
        setInteractionStates(prev => new Map(prev).set(postId, newState))
      }

      // Store interaction
      await storeComment(postId, content, mood)

      // Analytics
      if (enableAnalytics) {
        recordEngagement(postId, 'comment')
      }

      toast.success('Comment added! 💬', { duration: 1500 })

    } catch (error) {
      console.error('Failed to add comment:', error)
      toast.error('Failed to add comment')
    } finally {
      setPendingInteractions(prev => {
        const newSet = new Set(prev)
        newSet.delete(interactionId)
        return newSet
      })
    }
  }, [
    pendingInteractions,
    enableOptimisticUpdates,
    enableAnalytics,
    getInteractionState,
    storeComment,
    recordEngagement,
  ])

  // Advanced mood reaction
  const reactWithMood = useCallback(async (postId: string, mood: string) => {
    await toggleLike(postId, mood)
    toast.success(`Reacted with ${mood}! 🎭`, { duration: 1500 })
  }, [toggleLike])

  // Memory/bookmark functionality
  const addToMemory = useCallback(async (postId: string) => {
    // TODO: Implement memory/bookmark functionality
    toast.success('Added to memories! 📚', { duration: 1500 })
  }, [])

  // Save post functionality
  const savePost = useCallback(async (postId: string) => {
    // TODO: Implement save functionality
    toast.success('Post saved! 💾', { duration: 1500 })
  }, [])

  // Report post functionality
  const reportPost = useCallback(async (postId: string, reason: string) => {
    // TODO: Implement report functionality
    toast.success('Report submitted', { duration: 1500 })
  }, [])

  // Create remix functionality
  const createRemix = useCallback(async (postId: string) => {
    // TODO: Implement remix functionality
    toast.success('Remix created! 🎵', { duration: 1500 })
  }, [])

  // Bulk operations
  const likeMultiplePosts = useCallback(async (postIds: string[]) => {
    const promises = postIds.map(postId => toggleLike(postId))
    await Promise.allSettled(promises)
    toast.success(`Liked ${postIds.length} posts! ❤️`, { duration: 2000 })
  }, [toggleLike])

  const shareMultiplePosts = useCallback(async (postIds: string[], platform?: string) => {
    const promises = postIds.map(postId => sharePost(postId, platform))
    await Promise.allSettled(promises)
    toast.success(`Shared ${postIds.length} posts! 🚀`, { duration: 2000 })
  }, [sharePost])

  // Analytics tracking
  const trackEngagement = useCallback((postId: string, type: string, duration?: number) => {
    if (!enableAnalytics) return
    recordEngagement(postId, type, duration)
  }, [enableAnalytics, recordEngagement])

  // Get engagement score
  const getEngagementScore = useCallback((postId: string) => {
    return storeEngagementScore(postId)
  }, [storeEngagementScore])

  // Real-time subscription (placeholder)
  const subscribeToPostUpdates = useCallback((postId: string, callback: (update: any) => void) => {
    // TODO: Implement WebSocket subscription
    return () => {
      // Cleanup subscription
    }
  }, [])

  // Utility functions
  const isInteractionPending = useCallback((postId: string, type: InteractionType) => {
    return Array.from(pendingInteractions).some(id => 
      id.includes(postId) && id.includes(type.toLowerCase())
    )
  }, [pendingInteractions])

  const getInteractionHistory = useCallback((userId: string) => {
    // TODO: Implement interaction history retrieval
    return []
  }, [])

  const clearPendingInteractions = useCallback(() => {
    setPendingInteractions(new Set())
  }, [])

  // Sync interactions periodically
  useEffect(() => {
    const interval = setInterval(() => {
      syncInteractions()
    }, 30000) // Sync every 30 seconds

    return () => clearInterval(interval)
  }, [syncInteractions])

  return {
    getInteractionState,
    toggleLike,
    toggleFollow,
    sharePost,
    addComment,
    savePost,
    reportPost,
    reactWithMood,
    addToMemory,
    createRemix,
    likeMultiplePosts,
    shareMultiplePosts,
    trackEngagement,
    getEngagementScore,
    subscribeToPostUpdates,
    isInteractionPending,
    getInteractionHistory,
    clearPendingInteractions,
  }
}
