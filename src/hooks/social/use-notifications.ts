"use client"

import { useCallback, useEffect } from 'react'
import { toast } from 'sonner'
import { useSocialStore } from '@/lib/stores/social-store'

interface UseNotificationsReturn {
  // Data fetching
  loadNotifications: () => Promise<void>
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  deleteNotification: (notificationId: string) => Promise<void>
  
  // Real-time
  subscribeToNotifications: () => void
  unsubscribeFromNotifications: () => void
  
  // State
  notifications: any[]
  unreadCount: number
  isLoading: boolean
}

export function useNotifications(): UseNotificationsReturn {
  const {
    notifications,
    unreadCount,
    loadingNotifications: isLoading,
    addNotification,
    markNotificationRead,
    markAllNotificationsRead,
    removeNotification,
    loadNotifications: setNotifications,
    setLoadingNotifications,
  } = useSocialStore()

  const loadNotifications = useCallback(async () => {
    setLoadingNotifications(true)
    
    try {
      const response = await fetch('/api/notifications')
      
      if (!response.ok) {
        throw new Error('Failed to load notifications')
      }

      const data = await response.json()
      setNotifications(data.notifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
      toast.error('Failed to load notifications')
      throw error
    } finally {
      setLoadingNotifications(false)
    }
  }, [setNotifications, setLoadingNotifications])

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }

      markNotificationRead(notificationId)
    } catch (error) {
      console.error('Error marking notification as read:', error)
      toast.error('Failed to mark notification as read')
      throw error
    }
  }, [markNotificationRead])

  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read')
      }

      markAllNotificationsRead()
      toast.success('All notifications marked as read')
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      toast.error('Failed to mark all notifications as read')
      throw error
    }
  }, [markAllNotificationsRead])

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete notification')
      }

      removeNotification(notificationId)
      toast.success('Notification deleted')
    } catch (error) {
      console.error('Error deleting notification:', error)
      toast.error('Failed to delete notification')
      throw error
    }
  }, [removeNotification])

  // Real-time notifications using Server-Sent Events
  const subscribeToNotifications = useCallback(() => {
    const eventSource = new EventSource('/api/notifications/stream')

    eventSource.onmessage = (event) => {
      try {
        const notification = JSON.parse(event.data)
        
        // Add to store
        addNotification(notification)
        
        // Show toast notification
        showNotificationToast(notification)
      } catch (error) {
        console.error('Error parsing notification:', error)
      }
    }

    eventSource.onerror = (error) => {
      console.error('Notification stream error:', error)
      // Attempt to reconnect after 5 seconds
      setTimeout(() => {
        if (eventSource.readyState === EventSource.CLOSED) {
          subscribeToNotifications()
        }
      }, 5000)
    }

    // Store reference for cleanup
    ;(window as any).__notificationEventSource = eventSource

    return () => {
      eventSource.close()
    }
  }, [addNotification])

  const unsubscribeFromNotifications = useCallback(() => {
    const eventSource = (window as any).__notificationEventSource
    if (eventSource) {
      eventSource.close()
      delete (window as any).__notificationEventSource
    }
  }, [])

  const showNotificationToast = (notification: any) => {
    const { type, fromUser, message } = notification

    let icon = '🔔'
    switch (type) {
      case 'like':
        icon = '❤️'
        break
      case 'comment':
        icon = '💬'
        break
      case 'follow':
        icon = '👤'
        break
      case 'share':
        icon = '🔄'
        break
      case 'memory':
        icon = '💾'
        break
      case 'mood_match':
        icon = '🎭'
        break
    }

    toast(message, {
      description: `From ${fromUser.displayName || fromUser.username}`,
      action: {
        label: 'View',
        onClick: () => {
          // Navigate to relevant page based on notification type
          if (notification.postId) {
            window.location.href = `/post/${notification.postId}`
          } else if (fromUser.username) {
            window.location.href = `/profile/${fromUser.username}`
          }
        },
      },
    })
  }

  // Auto-load notifications on mount
  useEffect(() => {
    if (notifications.length === 0) {
      loadNotifications()
    }
  }, [loadNotifications, notifications.length])

  // Subscribe to real-time notifications on mount
  useEffect(() => {
    subscribeToNotifications()
    
    return () => {
      unsubscribeFromNotifications()
    }
  }, [subscribeToNotifications, unsubscribeFromNotifications])

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  return {
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    subscribeToNotifications,
    unsubscribeFromNotifications,
    notifications,
    unreadCount,
    isLoading,
  }
}
