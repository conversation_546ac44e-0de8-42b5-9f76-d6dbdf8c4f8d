"use client"

import { useCallback } from 'react'
import { toast } from 'sonner'
import { useSocialStore } from '@/lib/stores/social-store'

interface UseFollowSystemReturn {
  // Follow actions
  followUser: (userId: string) => Promise<void>
  unfollowUser: (userId: string) => Promise<void>
  
  // Data fetching
  loadFollowers: (userId?: string) => Promise<void>
  loadFollowing: (userId?: string) => Promise<void>
  loadSuggestedUsers: () => Promise<void>
  
  // State getters
  isFollowing: (userId: string) => boolean
  isFollower: (userId: string) => boolean
  getConnectionType: (userId: string) => 'follower' | 'following' | 'mutual' | 'none'
  
  // Loading states
  isLoadingFollow: boolean
  isLoadingFollowers: boolean
  isLoadingFollowing: boolean
  isLoadingSuggestions: boolean
}

export function useFollowSystem(): UseFollowSystemReturn {
  const {
    addFollowing,
    removeFollowing,
    loadFollowers: setFollowers,
    loadFollowing: setFollowing,
    setSuggestedUsers,
    setLoadingFollowers,
    setLoadingFollowing,
    setLoadingSuggestions,
    isFollowing: checkIsFollowing,
    isFollower: checkIsFollower,
    getConnectionType: getConnectionTypeFromStore,
    loadingFollowers,
    loadingFollowing,
    loadingSuggestions,
  } = useSocialStore()

  const followUser = useCallback(async (userId: string) => {
    try {
      // Optimistic update
      const response = await fetch('/api/user/follow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ followingId: userId }),
      })

      if (!response.ok) {
        throw new Error('Failed to follow user')
      }

      const data = await response.json()
      
      // Update store with user data
      addFollowing({
        id: userId,
        username: data.user.username,
        displayName: data.user.displayName,
        bio: data.user.bio,
        avatarUrl: data.user.avatar,
        isVerified: data.user.isVerified,
        followerCount: data.user.followerCount,
        followingCount: data.user.followingCount,
        postCount: data.user.postCount,
        createdAt: new Date(data.user.createdAt),
      })

      toast.success(`Now following ${data.user.displayName || data.user.username}`)
    } catch (error) {
      console.error('Error following user:', error)
      toast.error('Failed to follow user')
      throw error
    }
  }, [addFollowing])

  const unfollowUser = useCallback(async (userId: string) => {
    try {
      const response = await fetch('/api/user/follow', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ followingId: userId }),
      })

      if (!response.ok) {
        throw new Error('Failed to unfollow user')
      }

      // Update store
      removeFollowing(userId)

      toast.success('Unfollowed successfully')
    } catch (error) {
      console.error('Error unfollowing user:', error)
      toast.error('Failed to unfollow user')
      throw error
    }
  }, [removeFollowing])

  const loadFollowers = useCallback(async (userId?: string) => {
    setLoadingFollowers(true)
    
    try {
      const params = new URLSearchParams()
      if (userId) params.append('userId', userId)
      
      const response = await fetch(`/api/user/followers?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to load followers')
      }

      const data = await response.json()
      
      const followers = data.followers.map((follower: any) => ({
        id: follower.id,
        username: follower.username,
        displayName: follower.displayName,
        bio: follower.bio,
        avatarUrl: follower.avatar,
        isVerified: follower.isVerified,
        followerCount: follower.followerCount,
        followingCount: follower.followingCount,
        postCount: follower.postCount,
        createdAt: new Date(follower.createdAt),
      }))

      setFollowers(followers)
    } catch (error) {
      console.error('Error loading followers:', error)
      toast.error('Failed to load followers')
      throw error
    } finally {
      setLoadingFollowers(false)
    }
  }, [setFollowers, setLoadingFollowers])

  const loadFollowing = useCallback(async (userId?: string) => {
    setLoadingFollowing(true)
    
    try {
      const params = new URLSearchParams()
      if (userId) params.append('userId', userId)
      
      const response = await fetch(`/api/user/following?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to load following')
      }

      const data = await response.json()
      
      const following = data.following.map((user: any) => ({
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        bio: user.bio,
        avatarUrl: user.avatar,
        isVerified: user.isVerified,
        followerCount: user.followerCount,
        followingCount: user.followingCount,
        postCount: user.postCount,
        createdAt: new Date(user.createdAt),
      }))

      setFollowing(following)
    } catch (error) {
      console.error('Error loading following:', error)
      toast.error('Failed to load following')
      throw error
    } finally {
      setLoadingFollowing(false)
    }
  }, [setFollowing, setLoadingFollowing])

  const loadSuggestedUsers = useCallback(async () => {
    setLoadingSuggestions(true)
    
    try {
      const response = await fetch('/api/user/suggestions')
      
      if (!response.ok) {
        throw new Error('Failed to load suggested users')
      }

      const data = await response.json()
      
      const suggestions = data.suggestions.map((user: any) => ({
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        bio: user.bio,
        avatarUrl: user.avatar,
        isVerified: user.isVerified,
        followerCount: user.followerCount,
        followingCount: user.followingCount,
        postCount: user.postCount,
        createdAt: new Date(user.createdAt),
      }))

      setSuggestedUsers(suggestions)
    } catch (error) {
      console.error('Error loading suggested users:', error)
      toast.error('Failed to load suggestions')
      throw error
    } finally {
      setLoadingSuggestions(false)
    }
  }, [setSuggestedUsers, setLoadingSuggestions])

  const isFollowing = useCallback((userId: string) => {
    return checkIsFollowing(userId)
  }, [checkIsFollowing])

  const isFollower = useCallback((userId: string) => {
    return checkIsFollower(userId)
  }, [checkIsFollower])

  const getConnectionType = useCallback((userId: string) => {
    return getConnectionTypeFromStore(userId)
  }, [getConnectionTypeFromStore])

  return {
    followUser,
    unfollowUser,
    loadFollowers,
    loadFollowing,
    loadSuggestedUsers,
    isFollowing,
    isFollower,
    getConnectionType,
    isLoadingFollow: false, // You could add this state if needed
    isLoadingFollowers: loadingFollowers,
    isLoadingFollowing: loadingFollowing,
    isLoadingSuggestions: loadingSuggestions,
  }
}
