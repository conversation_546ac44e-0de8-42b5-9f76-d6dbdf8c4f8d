"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { MediaSource } from '@/lib/player'

interface PlaylistItem {
  id: string
  title: string
  artist?: string
  album?: string
  duration?: number
  source: MediaSource
  metadata?: any
}

interface Playlist {
  id: string
  name: string
  description?: string
  items: PlaylistItem[]
  currentIndex: number
  isShuffled: boolean
  repeatMode: 'none' | 'one' | 'all'
}

interface UsePlaylistOptions {
  autoPlay?: boolean
  shuffle?: boolean
  repeat?: 'none' | 'one' | 'all'
  onTrackChange?: (track: PlaylistItem, index: number) => void
  onPlaylistEnd?: () => void
}

export function usePlaylist(
  playlistId?: string,
  options: UsePlaylistOptions = {}
) {
  const [playlist, setPlaylist] = useState<Playlist | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTrack, setCurrentTrack] = useState<PlaylistItem | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [shuffledIndices, setShuffledIndices] = useState<number[]>([])
  const originalOrderRef = useRef<number[]>([])

  // Load playlist data
  useEffect(() => {
    if (!playlistId) return

    const loadPlaylist = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch(`/api/player/playlists/${playlistId}`)
        if (!response.ok) {
          throw new Error('Failed to load playlist')
        }
        
        const data = await response.json()
        setPlaylist(data)
        
        if (data.items.length > 0) {
          setCurrentTrack(data.items[0])
          setCurrentIndex(0)
          originalOrderRef.current = data.items.map((_: any, index: number) => index)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load playlist')
      } finally {
        setLoading(false)
      }
    }

    loadPlaylist()
  }, [playlistId])

  // Initialize shuffle if enabled
  useEffect(() => {
    if (playlist && options.shuffle) {
      shuffle()
    }
  }, [playlist, options.shuffle])

  // Shuffle function
  const shuffle = useCallback(() => {
    if (!playlist) return

    const indices = [...originalOrderRef.current]
    for (let i = indices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[indices[i], indices[j]] = [indices[j], indices[i]]
    }
    
    setShuffledIndices(indices)
    setPlaylist(prev => prev ? { ...prev, isShuffled: true } : null)
  }, [playlist])

  // Unshuffle function
  const unshuffle = useCallback(() => {
    setShuffledIndices([])
    setPlaylist(prev => prev ? { ...prev, isShuffled: false } : null)
  }, [])

  // Get current playing order
  const getPlayingOrder = useCallback(() => {
    if (!playlist) return []
    return playlist.isShuffled ? shuffledIndices : originalOrderRef.current
  }, [playlist, shuffledIndices])

  // Navigate to specific track
  const goToTrack = useCallback((index: number) => {
    if (!playlist || index < 0 || index >= playlist.items.length) return

    const track = playlist.items[index]
    setCurrentTrack(track)
    setCurrentIndex(index)
    options.onTrackChange?.(track, index)
  }, [playlist, options])

  // Next track
  const nextTrack = useCallback(() => {
    if (!playlist) return

    const playingOrder = getPlayingOrder()
    const currentOrderIndex = playingOrder.indexOf(currentIndex)
    
    if (currentOrderIndex === -1) return

    let nextOrderIndex = currentOrderIndex + 1

    // Handle repeat modes
    if (nextOrderIndex >= playingOrder.length) {
      if (playlist.repeatMode === 'all') {
        nextOrderIndex = 0
      } else if (playlist.repeatMode === 'one') {
        nextOrderIndex = currentOrderIndex // Stay on same track
      } else {
        // End of playlist
        options.onPlaylistEnd?.()
        return
      }
    }

    const nextIndex = playingOrder[nextOrderIndex]
    goToTrack(nextIndex)
  }, [playlist, currentIndex, getPlayingOrder, goToTrack, options])

  // Previous track
  const previousTrack = useCallback(() => {
    if (!playlist) return

    const playingOrder = getPlayingOrder()
    const currentOrderIndex = playingOrder.indexOf(currentIndex)
    
    if (currentOrderIndex === -1) return

    let prevOrderIndex = currentOrderIndex - 1

    // Handle wrap around
    if (prevOrderIndex < 0) {
      if (playlist.repeatMode === 'all') {
        prevOrderIndex = playingOrder.length - 1
      } else {
        prevOrderIndex = 0 // Stay at first track
      }
    }

    const prevIndex = playingOrder[prevOrderIndex]
    goToTrack(prevIndex)
  }, [playlist, currentIndex, getPlayingOrder, goToTrack])

  // Set repeat mode
  const setRepeatMode = useCallback((mode: 'none' | 'one' | 'all') => {
    setPlaylist(prev => prev ? { ...prev, repeatMode: mode } : null)
  }, [])

  // Toggle shuffle
  const toggleShuffle = useCallback(() => {
    if (!playlist) return

    if (playlist.isShuffled) {
      unshuffle()
    } else {
      shuffle()
    }
  }, [playlist, shuffle, unshuffle])

  // Add track to playlist
  const addTrack = useCallback((track: PlaylistItem, index?: number) => {
    if (!playlist) return

    const newItems = [...playlist.items]
    const insertIndex = index !== undefined ? index : newItems.length
    newItems.splice(insertIndex, 0, track)

    setPlaylist(prev => prev ? { ...prev, items: newItems } : null)
    originalOrderRef.current = newItems.map((_, i) => i)
  }, [playlist])

  // Remove track from playlist
  const removeTrack = useCallback((index: number) => {
    if (!playlist || index < 0 || index >= playlist.items.length) return

    const newItems = playlist.items.filter((_, i) => i !== index)
    setPlaylist(prev => prev ? { ...prev, items: newItems } : null)
    originalOrderRef.current = newItems.map((_, i) => i)

    // Adjust current index if necessary
    if (index === currentIndex && newItems.length > 0) {
      const newIndex = Math.min(currentIndex, newItems.length - 1)
      goToTrack(newIndex)
    } else if (index < currentIndex) {
      setCurrentIndex(prev => prev - 1)
    }
  }, [playlist, currentIndex, goToTrack])

  return {
    playlist,
    loading,
    error,
    isPlaying,
    currentTrack,
    currentIndex,
    nextTrack,
    previousTrack,
    goToTrack,
    shuffle,
    unshuffle,
    toggleShuffle,
    setRepeatMode,
    addTrack,
    removeTrack,
    setIsPlaying
  }
}

// HVPPY-specific playlist hook
export function useHVPPYPlaylist(playlistId: string, options: UsePlaylistOptions = {}) {
  return usePlaylist(playlistId, options)
}

// Mood-based playlist hook
export function useMoodPlaylist(mood: string, options: UsePlaylistOptions = {}) {
  const [moodPlaylistId, setMoodPlaylistId] = useState<string | null>(null)

  // Load mood-based playlist
  useEffect(() => {
    const loadMoodPlaylist = async () => {
      try {
        const response = await fetch(`/api/player/playlists/mood/${mood}`)
        if (response.ok) {
          const data = await response.json()
          setMoodPlaylistId(data.playlistId)
        }
      } catch (error) {
        console.error('Failed to load mood playlist:', error)
      }
    }

    if (mood) {
      loadMoodPlaylist()
    }
  }, [mood])

  return usePlaylist(moodPlaylistId || undefined, options)
}
