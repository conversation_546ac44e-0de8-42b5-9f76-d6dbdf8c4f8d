"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { createAudioPlayer, AudioPlayer, MediaSource, PlayerConfig, UseAudioPlayerReturn } from '@/lib/player'

interface UseAudioPlayerOptions {
  element?: HTMLAudioElement
  source?: MediaSource
  config?: Partial<PlayerConfig>
  onPlayerReady?: (player: AudioPlayer) => void
  onStateChange?: (state: any) => void
  onTimeUpdate?: (currentTime: number) => void
  onError?: (error: any) => void
}

export function useAudioPlayer(options: UseAudioPlayerOptions = {}): UseAudioPlayerReturn {
  const [player, setPlayer] = useState<AudioPlayer | null>(null)
  const [state, setState] = useState({
    state: 'idle' as any,
    currentTime: 0,
    duration: 0,
    buffered: null,
    volume: 1,
    isMuted: false,
    playbackRate: 1,
    quality: 'auto',
    error: null
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize player when element is available
  useEffect(() => {
    if (!options.element) return

    try {
      const audioPlayer = createAudioPlayer(options.element, options.config)
      setPlayer(audioPlayer)

      // Setup event listeners
      audioPlayer.on('onStateChange', (newState: any) => {
        setState(newState)
        setIsLoading(newState.state === 'loading' || newState.state === 'buffering')
        options.onStateChange?.(newState)
      })

      audioPlayer.on('onTimeUpdate', (currentTime: number) => {
        setState(prev => ({ ...prev, currentTime }))
        options.onTimeUpdate?.(currentTime)
      })

      audioPlayer.on('onError', (err: any) => {
        setError(err)
        options.onError?.(err)
      })

      options.onPlayerReady?.(audioPlayer)

      return () => {
        audioPlayer.destroy()
      }
    } catch (err) {
      setError('Failed to initialize audio player')
      console.error('Audio player initialization error:', err)
    }
  }, [options.element, options.config])

  // Load source when it changes
  useEffect(() => {
    if (player && options.source) {
      setIsLoading(true)
      setError(null)
      
      player.load(options.source).catch((err) => {
        setError('Failed to load audio source')
        console.error('Audio load error:', err)
      })
    }
  }, [player, options.source])

  const play = useCallback(async () => {
    if (player) {
      try {
        await player.play()
      } catch (err) {
        setError('Failed to play audio')
        throw err
      }
    }
  }, [player])

  const pause = useCallback(() => {
    if (player) {
      player.pause()
    }
  }, [player])

  const seek = useCallback((time: number) => {
    if (player) {
      player.seek(time)
    }
  }, [player])

  const setVolume = useCallback((volume: number) => {
    if (player) {
      player.setVolume(volume)
    }
  }, [player])

  const toggleMute = useCallback(() => {
    if (player) {
      player.setMuted(!state.isMuted)
    }
  }, [player, state.isMuted])

  const getFrequencyData = useCallback(() => {
    if (player) {
      return player.getFrequencyData()
    }
    return null
  }, [player])

  const setEqualizer = useCallback((bands: number[]) => {
    if (player) {
      player.setEqualizer(bands)
    }
  }, [player])

  return {
    player,
    state,
    isLoading,
    error,
    play,
    pause,
    seek,
    setVolume,
    toggleMute,
    getFrequencyData,
    setEqualizer
  }
}

// HVPPY-specific audio player hook
export function useHVPPYAudioPlayer(contentId: string, options: UseAudioPlayerOptions = {}) {
  const [contentData, setContentData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // Load content data
  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/player/content/audio/${contentId}`)
        if (response.ok) {
          const data = await response.json()
          setContentData(data)
        }
      } catch (error) {
        console.error('Failed to load audio content:', error)
      } finally {
        setLoading(false)
      }
    }

    if (contentId) {
      loadContent()
    }
  }, [contentId])

  const playerHook = useAudioPlayer({
    ...options,
    source: contentData?.source
  })

  return {
    ...playerHook,
    contentData,
    loading
  }
}

// Audio player with mood-based EQ
interface MoodEQSettings {
  [mood: string]: number[] // EQ band values for each mood
}

const MOOD_EQ_PRESETS: MoodEQSettings = {
  happy: [2, 4, 3, 2, 1, 0, 1, 2, 3, 2],
  sad: [-1, -2, 0, 1, 2, 1, 0, -1, -2, -1],
  energetic: [4, 3, 2, 1, 0, 1, 2, 3, 4, 3],
  calm: [-2, -1, 0, 1, 2, 2, 1, 0, -1, -2],
  romantic: [1, 2, 3, 2, 1, 0, 1, 2, 1, 0],
  focus: [0, 0, 1, 2, 1, 0, 0, 0, 0, 0],
  party: [3, 2, 1, 0, 1, 2, 3, 4, 3, 2],
  chill: [-1, 0, 1, 2, 1, 0, 0, -1, -1, 0]
}

interface UseAudioPlayerWithMoodEQOptions extends UseAudioPlayerOptions {
  enableVisualizer?: boolean
}

export function useAudioPlayerWithMoodEQ(
  mood?: string,
  options: UseAudioPlayerWithMoodEQOptions = {}
) {
  const playerHook = useAudioPlayer(options)
  const [currentMood, setCurrentMood] = useState<string | undefined>(mood)
  const [visualizerData, setVisualizerData] = useState<Uint8Array | null>(null)

  // Apply mood EQ when mood changes
  useEffect(() => {
    if (playerHook.player && currentMood && MOOD_EQ_PRESETS[currentMood]) {
      playerHook.setEqualizer(MOOD_EQ_PRESETS[currentMood])
    }
  }, [playerHook.player, currentMood, playerHook.setEqualizer])

  // Update visualizer data
  useEffect(() => {
    if (!options.enableVisualizer || !playerHook.player) return

    const updateVisualizer = () => {
      const data = playerHook.getFrequencyData()
      setVisualizerData(data)
    }

    const interval = setInterval(updateVisualizer, 50) // 20fps
    return () => clearInterval(interval)
  }, [playerHook.player, options.enableVisualizer, playerHook.getFrequencyData])

  const changeMood = useCallback((newMood: string) => {
    setCurrentMood(newMood)
  }, [])

  const resetEQ = useCallback(() => {
    if (playerHook.player) {
      playerHook.setEqualizer([0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
    }
  }, [playerHook.player, playerHook.setEqualizer])

  return {
    ...playerHook,
    currentMood,
    visualizerData,
    changeMood,
    resetEQ,
    availableMoods: Object.keys(MOOD_EQ_PRESETS)
  }
}
