"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { createVideoPlayer, VideoPlayer, MediaSource, PlayerConfig, UseVideoPlayerReturn } from '@/lib/player'

interface UseVideoPlayerOptions {
  element?: HTMLVideoElement
  source?: MediaSource
  config?: Partial<PlayerConfig>
  onPlayerReady?: (player: VideoPlayer) => void
  onStateChange?: (state: any) => void
  onTimeUpdate?: (currentTime: number) => void
  onError?: (error: any) => void
}

export function useVideoPlayer(options: UseVideoPlayerOptions = {}): UseVideoPlayerReturn {
  const [player, setPlayer] = useState<VideoPlayer | null>(null)
  const [state, setState] = useState({
    state: 'idle' as any,
    currentTime: 0,
    duration: 0,
    buffered: null,
    volume: 1,
    isMuted: false,
    playbackRate: 1,
    quality: 'auto',
    error: null
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize player when element is available
  useEffect(() => {
    if (!options.element) return

    try {
      const videoPlayer = createVideoPlayer(options.element, options.config)
      setPlayer(videoPlayer)

      // Setup event listeners
      videoPlayer.on('onStateChange', (newState: any) => {
        setState(newState)
        setIsLoading(newState.state === 'loading' || newState.state === 'buffering')
        options.onStateChange?.(newState)
      })

      videoPlayer.on('onTimeUpdate', (currentTime: number) => {
        setState(prev => ({ ...prev, currentTime }))
        options.onTimeUpdate?.(currentTime)
      })

      videoPlayer.on('onError', (err: any) => {
        setError(err)
        options.onError?.(err)
      })

      options.onPlayerReady?.(videoPlayer)

      return () => {
        videoPlayer.destroy()
      }
    } catch (err) {
      setError('Failed to initialize video player')
      console.error('Video player initialization error:', err)
    }
  }, [options.element, options.config])

  // Load source when it changes
  useEffect(() => {
    if (player && options.source) {
      setIsLoading(true)
      setError(null)
      
      player.load(options.source).catch((err) => {
        setError('Failed to load video source')
        console.error('Video load error:', err)
      })
    }
  }, [player, options.source])

  const play = useCallback(async () => {
    if (player) {
      try {
        await player.play()
      } catch (err) {
        setError('Failed to play video')
        throw err
      }
    }
  }, [player])

  const pause = useCallback(() => {
    if (player) {
      player.pause()
    }
  }, [player])

  const seek = useCallback((time: number) => {
    if (player) {
      player.seek(time)
    }
  }, [player])

  const setVolume = useCallback((volume: number) => {
    if (player) {
      player.setVolume(volume)
    }
  }, [player])

  const toggleMute = useCallback(() => {
    if (player) {
      player.setMuted(!state.isMuted)
    }
  }, [player, state.isMuted])

  const setQuality = useCallback((quality: any) => {
    if (player) {
      player.setQuality(quality)
    }
  }, [player])

  const enterFullscreen = useCallback(async () => {
    if (player) {
      try {
        await player.enterFullscreen()
      } catch (err) {
        setError('Failed to enter fullscreen')
        throw err
      }
    }
  }, [player])

  const exitFullscreen = useCallback(async () => {
    if (player) {
      try {
        await player.exitFullscreen()
      } catch (err) {
        setError('Failed to exit fullscreen')
        throw err
      }
    }
  }, [player])

  return {
    player,
    state,
    isLoading,
    error,
    play,
    pause,
    seek,
    setVolume,
    toggleMute,
    setQuality,
    enterFullscreen,
    exitFullscreen
  }
}

// Hook with intersection observer for auto-play
interface UseVideoPlayerWithIntersectionOptions extends UseVideoPlayerOptions {
  threshold?: number
  rootMargin?: string
}

export function useVideoPlayerWithIntersection(
  containerRef: React.RefObject<HTMLElement>,
  options: UseVideoPlayerWithIntersectionOptions = {}
) {
  const [isInView, setIsInView] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)
  
  const playerHook = useVideoPlayer(options)

  // Setup intersection observer
  useEffect(() => {
    if (!containerRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const inView = entry.isIntersecting && entry.intersectionRatio >= (options.threshold || 0.5)
          setIsInView(inView)
          
          if (inView) {
            playerHook.play().catch(console.error)
          } else {
            playerHook.pause()
          }
        })
      },
      {
        threshold: options.threshold || 0.5,
        rootMargin: options.rootMargin || '0px'
      }
    )

    observerRef.current.observe(containerRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [containerRef, options.threshold, options.rootMargin, playerHook])

  return {
    ...playerHook,
    isInView
  }
}

// HVPPY-specific video player hook
export function useHVPPYVideoPlayer(contentId: string, options: UseVideoPlayerOptions = {}) {
  const [contentData, setContentData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // Load content data
  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/player/content/video/${contentId}`)
        if (response.ok) {
          const data = await response.json()
          setContentData(data)
        }
      } catch (error) {
        console.error('Failed to load video content:', error)
      } finally {
        setLoading(false)
      }
    }

    if (contentId) {
      loadContent()
    }
  }, [contentId])

  const playerHook = useVideoPlayer({
    ...options,
    source: contentData?.source
  })

  return {
    ...playerHook,
    contentData,
    loading
  }
}

// Vertical video player hook (for TikTok-style feeds)
export function useVerticalVideoPlayer(options: UseVideoPlayerOptions = {}) {
  return useVideoPlayer({
    ...options,
    config: {
      autoplay: false,
      muted: true,
      loop: true,
      controls: false,
      playsInline: true,
      ...options.config
    }
  })
}
