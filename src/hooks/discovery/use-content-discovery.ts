"use client"

import { useCallback, useEffect, useState } from 'react'
import { useFeedStore } from '@/lib/stores/feed-store'
import { useInteractionStore } from '@/lib/stores/interaction-store'
import { useSocialStore } from '@/lib/stores/social-store'
import { useUserPreferencesStore } from '@/lib/stores/user-preferences-store'
import { FeedItem, FeedType } from '@/types/feed'
import { toast } from 'sonner'

interface DiscoveryAlgorithm {
  id: string
  name: string
  description: string
  weight: number
  enabled: boolean
}

interface ContentScore {
  postId: string
  score: number
  reasons: string[]
  algorithm: string
}

interface UseContentDiscoveryReturn {
  // Discovery methods
  discoverContent: (moods?: string[], limit?: number) => Promise<FeedItem[]>
  getTrendingContent: (timeframe?: 'hour' | 'day' | 'week') => Promise<FeedItem[]>
  getPersonalizedFeed: (userId?: string) => Promise<FeedItem[]>
  getMoodBasedContent: (moods: string[]) => Promise<FeedItem[]>
  
  // Algorithm management
  algorithms: DiscoveryAlgorithm[]
  updateAlgorithmWeight: (algorithmId: string, weight: number) => void
  toggleAlgorithm: (algorithmId: string, enabled: boolean) => void
  
  // Analytics
  getDiscoveryMetrics: () => {
    totalRecommendations: number
    clickThroughRate: number
    engagementRate: number
    diversityScore: number
  }
  
  // State
  isLoading: boolean
  lastUpdated: Date | null
}

const defaultAlgorithms: DiscoveryAlgorithm[] = [
  {
    id: 'mood_matching',
    name: 'Mood Matching',
    description: 'Content that matches your current mood preferences',
    weight: 0.3,
    enabled: true,
  },
  {
    id: 'social_signals',
    name: 'Social Signals',
    description: 'Content liked by people you follow',
    weight: 0.25,
    enabled: true,
  },
  {
    id: 'engagement_based',
    name: 'Engagement Based',
    description: 'Highly engaging content across the platform',
    weight: 0.2,
    enabled: true,
  },
  {
    id: 'similarity_based',
    name: 'Content Similarity',
    description: 'Content similar to what you\'ve engaged with',
    weight: 0.15,
    enabled: true,
  },
  {
    id: 'temporal_trending',
    name: 'Trending Now',
    description: 'Currently trending content',
    weight: 0.1,
    enabled: true,
  },
]

export function useContentDiscovery(): UseContentDiscoveryReturn {
  const [algorithms, setAlgorithms] = useState<DiscoveryAlgorithm[]>(defaultAlgorithms)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  
  const { items: feedItems, addItems, setItems } = useFeedStore()
  const { interactionHistory, getMostEngagedPosts } = useInteractionStore()
  const { following, currentUser } = useSocialStore()
  const { content: contentPreferences } = useUserPreferencesStore()

  // Mood-based content discovery
  const getMoodBasedContent = useCallback(async (moods: string[]): Promise<FeedItem[]> => {
    try {
      const response = await fetch('/api/discovery/mood-based', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ moods, limit: 20 }),
      })
      
      if (!response.ok) throw new Error('Failed to fetch mood-based content')
      
      const data = await response.json()
      return data.content
    } catch (error) {
      console.error('Error fetching mood-based content:', error)
      return []
    }
  }, [])

  // Social signals algorithm
  const getSocialSignalsContent = useCallback(async (): Promise<ContentScore[]> => {
    if (following.size === 0) return []
    
    try {
      const followingIds = Array.from(following.keys())
      const response = await fetch('/api/discovery/social-signals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ followingIds, limit: 50 }),
      })
      
      if (!response.ok) throw new Error('Failed to fetch social signals')
      
      const data = await response.json()
      return data.scores.map((score: any) => ({
        ...score,
        algorithm: 'social_signals',
        reasons: [`Liked by ${score.likedByFollowing} people you follow`],
      }))
    } catch (error) {
      console.error('Error fetching social signals:', error)
      return []
    }
  }, [following])

  // Engagement-based algorithm
  const getEngagementBasedContent = useCallback(async (): Promise<ContentScore[]> => {
    try {
      const response = await fetch('/api/discovery/engagement-based?limit=50')
      
      if (!response.ok) throw new Error('Failed to fetch engagement-based content')
      
      const data = await response.json()
      return data.scores.map((score: any) => ({
        ...score,
        algorithm: 'engagement_based',
        reasons: [`High engagement: ${score.engagementRate}% engagement rate`],
      }))
    } catch (error) {
      console.error('Error fetching engagement-based content:', error)
      return []
    }
  }, [])

  // Content similarity algorithm
  const getSimilarityBasedContent = useCallback(async (): Promise<ContentScore[]> => {
    const engagedPosts = getMostEngagedPosts(10)
    if (engagedPosts.length === 0) return []
    
    try {
      const response = await fetch('/api/discovery/similarity-based', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ seedPosts: engagedPosts, limit: 50 }),
      })
      
      if (!response.ok) throw new Error('Failed to fetch similar content')
      
      const data = await response.json()
      return data.scores.map((score: any) => ({
        ...score,
        algorithm: 'similarity_based',
        reasons: [`Similar to content you've engaged with`],
      }))
    } catch (error) {
      console.error('Error fetching similar content:', error)
      return []
    }
  }, [getMostEngagedPosts])

  // Trending content algorithm
  const getTrendingContent = useCallback(async (timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<FeedItem[]> => {
    try {
      const response = await fetch(`/api/discovery/trending?timeframe=${timeframe}&limit=20`)
      
      if (!response.ok) throw new Error('Failed to fetch trending content')
      
      const data = await response.json()
      return data.content
    } catch (error) {
      console.error('Error fetching trending content:', error)
      return []
    }
  }, [])

  // Combine all algorithms for personalized feed
  const getPersonalizedFeed = useCallback(async (userId?: string): Promise<FeedItem[]> => {
    setIsLoading(true)
    
    try {
      const enabledAlgorithms = algorithms.filter(alg => alg.enabled)
      const allScores: ContentScore[] = []
      
      // Run all enabled algorithms in parallel
      const algorithmPromises = enabledAlgorithms.map(async (algorithm) => {
        switch (algorithm.id) {
          case 'mood_matching':
            if (contentPreferences.preferredMoods.length > 0) {
              const content = await getMoodBasedContent(contentPreferences.preferredMoods)
              return content.map(item => ({
                postId: item.id,
                score: 0.8 * algorithm.weight,
                reasons: [`Matches your mood preferences: ${contentPreferences.preferredMoods.join(', ')}`],
                algorithm: algorithm.id,
              }))
            }
            return []
            
          case 'social_signals':
            return await getSocialSignalsContent()
            
          case 'engagement_based':
            return await getEngagementBasedContent()
            
          case 'similarity_based':
            return await getSimilarityBasedContent()
            
          case 'temporal_trending':
            const trending = await getTrendingContent()
            return trending.map(item => ({
              postId: item.id,
              score: 0.6 * algorithm.weight,
              reasons: ['Currently trending'],
              algorithm: algorithm.id,
            }))
            
          default:
            return []
        }
      })
      
      const algorithmResults = await Promise.all(algorithmPromises)
      algorithmResults.forEach(scores => allScores.push(...scores))
      
      // Combine scores for the same posts
      const combinedScores = new Map<string, ContentScore>()
      
      allScores.forEach(score => {
        const existing = combinedScores.get(score.postId)
        if (existing) {
          existing.score += score.score
          existing.reasons.push(...score.reasons)
        } else {
          combinedScores.set(score.postId, { ...score })
        }
      })
      
      // Sort by combined score and fetch full content
      const topPostIds = Array.from(combinedScores.values())
        .sort((a, b) => b.score - a.score)
        .slice(0, 20)
        .map(score => score.postId)
      
      if (topPostIds.length === 0) {
        // Fallback to general trending content
        return await getTrendingContent()
      }
      
      // Fetch full post data
      const response = await fetch('/api/posts/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ postIds: topPostIds }),
      })
      
      if (!response.ok) throw new Error('Failed to fetch post data')
      
      const data = await response.json()
      setLastUpdated(new Date())
      
      return data.posts
      
    } catch (error) {
      console.error('Error generating personalized feed:', error)
      toast.error('Failed to load personalized content')
      return []
    } finally {
      setIsLoading(false)
    }
  }, [algorithms, contentPreferences, getMoodBasedContent, getSocialSignalsContent, getEngagementBasedContent, getSimilarityBasedContent, getTrendingContent])

  // Main discovery function
  const discoverContent = useCallback(async (moods?: string[], limit = 20): Promise<FeedItem[]> => {
    if (moods && moods.length > 0) {
      return await getMoodBasedContent(moods)
    }
    
    return await getPersonalizedFeed()
  }, [getMoodBasedContent, getPersonalizedFeed])

  // Algorithm management
  const updateAlgorithmWeight = useCallback((algorithmId: string, weight: number) => {
    setAlgorithms(prev => prev.map(alg => 
      alg.id === algorithmId ? { ...alg, weight } : alg
    ))
  }, [])

  const toggleAlgorithm = useCallback((algorithmId: string, enabled: boolean) => {
    setAlgorithms(prev => prev.map(alg => 
      alg.id === algorithmId ? { ...alg, enabled } : alg
    ))
  }, [])

  // Analytics
  const getDiscoveryMetrics = useCallback(() => {
    // This would typically come from analytics data
    return {
      totalRecommendations: feedItems.length,
      clickThroughRate: 0.15, // 15%
      engagementRate: 0.08, // 8%
      diversityScore: 0.75, // 75%
    }
  }, [feedItems.length])

  return {
    discoverContent,
    getTrendingContent,
    getPersonalizedFeed,
    getMoodBasedContent,
    algorithms,
    updateAlgorithmWeight,
    toggleAlgorithm,
    getDiscoveryMetrics,
    isLoading,
    lastUpdated,
  }
}
