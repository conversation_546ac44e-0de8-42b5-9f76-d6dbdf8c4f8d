import { MediaSource, PlayerConfig, PlayerState, PlayerStateInfo, BasePlayer } from './types'

export abstract class BaseMediaPlayer implements BasePlayer {
  protected element: HTMLMediaElement
  protected config: PlayerConfig
  protected events: Map<string, Function[]> = new Map()
  protected state: PlayerState = 'idle'
  protected currentSource: MediaSource | null = null

  constructor(element: HTMLMediaElement, config: Partial<PlayerConfig> = {}) {
    this.element = element
    this.config = {
      autoplay: false,
      muted: false,
      loop: false,
      controls: false,
      preload: 'metadata',
      playsInline: true,
      ...config
    }

    this.setupElement()
    this.setupEventListeners()
  }

  protected setupElement(): void {
    this.element.autoplay = this.config.autoplay
    this.element.muted = this.config.muted
    this.element.loop = this.config.loop
    this.element.controls = this.config.controls
    this.element.preload = this.config.preload

    // playsInline is only available on video elements
    if (this.element instanceof HTMLVideoElement) {
      this.element.playsInline = this.config.playsInline
    }

    if (this.config.crossOrigin) {
      this.element.crossOrigin = this.config.crossOrigin
    }
  }

  protected setupEventListeners(): void {
    this.element.addEventListener('loadstart', () => {
      this.state = 'loading'
      this.emit('onLoadStart')
    })

    this.element.addEventListener('loadedmetadata', () => {
      this.emit('onLoadedMetadata')
      this.emit('onDurationChange', this.element.duration)
    })

    this.element.addEventListener('loadeddata', () => {
      this.emit('onLoadedData')
    })

    this.element.addEventListener('canplay', () => {
      this.state = 'idle'
      this.emit('onCanPlay')
    })

    this.element.addEventListener('canplaythrough', () => {
      this.emit('onCanPlayThrough')
    })

    this.element.addEventListener('play', () => {
      this.state = 'playing'
      this.emit('onPlay')
    })

    this.element.addEventListener('pause', () => {
      this.state = 'paused'
      this.emit('onPause')
    })

    this.element.addEventListener('ended', () => {
      this.state = 'ended'
      this.emit('onEnded')
    })

    this.element.addEventListener('timeupdate', () => {
      this.emit('onTimeUpdate', this.element.currentTime)
    })

    this.element.addEventListener('volumechange', () => {
      this.emit('onVolumeChange', this.element.volume, this.element.muted)
    })

    this.element.addEventListener('seeking', () => {
      this.emit('onSeeking')
    })

    this.element.addEventListener('seeked', () => {
      this.emit('onSeeked')
    })

    this.element.addEventListener('waiting', () => {
      this.state = 'buffering'
      this.emit('onWaiting')
    })

    this.element.addEventListener('progress', () => {
      this.emit('onProgress', this.element.buffered)
    })

    this.element.addEventListener('error', () => {
      this.state = 'error'
      const error = this.element.error
      const errorMessage = error ? `Media error: ${error.message}` : 'Unknown media error'
      this.emit('onError', errorMessage)
    })
  }

  async load(source: MediaSource): Promise<void> {
    this.currentSource = source
    this.state = 'loading'
    
    return new Promise((resolve, reject) => {
      const handleCanPlay = () => {
        this.element.removeEventListener('canplay', handleCanPlay)
        this.element.removeEventListener('error', handleError)
        resolve()
      }

      const handleError = () => {
        this.element.removeEventListener('canplay', handleCanPlay)
        this.element.removeEventListener('error', handleError)
        reject(new Error('Failed to load media source'))
      }

      this.element.addEventListener('canplay', handleCanPlay)
      this.element.addEventListener('error', handleError)
      
      this.element.src = source.url
    })
  }

  async play(): Promise<void> {
    try {
      await this.element.play()
    } catch (error) {
      this.state = 'error'
      throw error
    }
  }

  pause(): void {
    this.element.pause()
  }

  stop(): void {
    this.element.pause()
    this.element.currentTime = 0
    this.state = 'idle'
  }

  seek(time: number): void {
    this.element.currentTime = Math.max(0, Math.min(time, this.element.duration || 0))
  }

  setVolume(volume: number): void {
    this.element.volume = Math.max(0, Math.min(1, volume))
  }

  setMuted(muted: boolean): void {
    this.element.muted = muted
  }

  setPlaybackRate(rate: number): void {
    this.element.playbackRate = Math.max(0.25, Math.min(4, rate))
  }

  getState(): PlayerStateInfo {
    return {
      state: this.state,
      currentTime: this.element.currentTime,
      duration: this.element.duration || 0,
      buffered: this.element.buffered,
      volume: this.element.volume,
      isMuted: this.element.muted,
      playbackRate: this.element.playbackRate,
      quality: this.currentSource?.quality || 'auto',
      error: this.state === 'error' ? 'Media error occurred' : null
    }
  }

  getCurrentTime(): number {
    return this.element.currentTime
  }

  getDuration(): number {
    return this.element.duration || 0
  }

  getVolume(): number {
    return this.element.volume
  }

  isMuted(): boolean {
    return this.element.muted
  }

  isPlaying(): boolean {
    return this.state === 'playing'
  }

  on(event: string, callback: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event)!.push(callback)
  }

  off(event: string, callback: Function): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  protected emit(event: string, ...args: any[]): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(...args))
    }
  }

  destroy(): void {
    // Remove all event listeners
    this.events.clear()
    
    // Stop and reset the element
    this.element.pause()
    this.element.src = ''
    this.element.load()
    
    this.state = 'idle'
    this.currentSource = null
  }
}
