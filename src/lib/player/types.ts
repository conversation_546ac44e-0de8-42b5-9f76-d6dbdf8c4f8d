export enum MediaType {
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
}

export interface MediaSource {
  id: string
  url: string
  type: MediaType
  format: string
  quality?: 'low' | 'medium' | 'high'
  bitrate?: number
  resolution?: string
}

export interface MediaMetadata {
  title?: string
  artist?: string
  album?: string
  duration?: number
  thumbnail?: string
  description?: string
  tags?: string[]
  moods?: string[]
}

export interface PlaybackState {
  isPlaying: boolean
  currentTime: number
  duration: number
  buffered: TimeRanges | null
  volume: number
  isMuted: boolean
  playbackRate: number
  quality: string
  error: string | null
}

export interface PlayerConfig {
  autoplay: boolean
  muted: boolean
  loop: boolean
  controls: boolean
  preload: 'none' | 'metadata' | 'auto'
  crossOrigin?: 'anonymous' | 'use-credentials'
  playsInline: boolean
}

export interface PlayerEvents {
  onPlay?: () => void
  onPause?: () => void
  onTimeUpdate?: (currentTime: number) => void
  onDurationChange?: (duration: number) => void
  onVolumeChange?: (volume: number, muted: boolean) => void
  onError?: (error: string) => void
  onLoadStart?: () => void
  onLoadedMetadata?: () => void
  onLoadedData?: () => void
  onCanPlay?: () => void
  onCanPlayThrough?: () => void
  onEnded?: () => void
  onSeeking?: () => void
  onSeeked?: () => void
  onWaiting?: () => void
  onProgress?: (buffered: TimeRanges) => void
}

// Player state types
export type PlayerState = 'idle' | 'loading' | 'buffering' | 'playing' | 'paused' | 'ended' | 'error'

export interface PlayerStateInfo {
  state: PlayerState
  currentTime: number
  duration: number
  buffered: TimeRanges | null
  volume: number
  isMuted: boolean
  playbackRate: number
  quality: string
  error: string | null
}

// Quality types
export type PlaybackQuality = 'auto' | 'high' | 'medium' | 'low'
export type AudioQuality = 'high' | 'medium' | 'low'

// Base player interface
export interface BasePlayer {
  load(source: MediaSource): Promise<void>
  play(): Promise<void>
  pause(): void
  stop(): void
  seek(time: number): void
  setVolume(volume: number): void
  setMuted(muted: boolean): void
  setPlaybackRate(rate: number): void
  getState(): PlayerStateInfo
  getCurrentTime(): number
  getDuration(): number
  getVolume(): number
  isMuted(): boolean
  isPlaying(): boolean
  destroy(): void
  on(event: string, callback: Function): void
  off(event: string, callback: Function): void
}

// Audio player specific interface
export interface AudioPlayer extends BasePlayer {
  getFrequencyData(): Uint8Array | null
  getWaveformData(): Float32Array | null
  setEqualizer(bands: number[]): void
  getAudioContext(): AudioContext | null
}

// Video player specific interface
export interface VideoPlayer extends BasePlayer {
  setQuality(quality: PlaybackQuality): void
  getAvailableQualities(): PlaybackQuality[]
  enterFullscreen(): Promise<void>
  exitFullscreen(): Promise<void>
  isFullscreen(): boolean
  setSubtitles(enabled: boolean): void
}

// Hook return types
export interface UseVideoPlayerReturn {
  player: VideoPlayer | null
  state: PlayerStateInfo
  isLoading: boolean
  error: string | null
  play: () => Promise<void>
  pause: () => void
  seek: (time: number) => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  setQuality: (quality: PlaybackQuality) => void
  enterFullscreen: () => Promise<void>
  exitFullscreen: () => Promise<void>
}

export interface UseAudioPlayerReturn {
  player: AudioPlayer | null
  state: PlayerStateInfo
  isLoading: boolean
  error: string | null
  play: () => Promise<void>
  pause: () => void
  seek: (time: number) => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  getFrequencyData: () => Uint8Array | null
  setEqualizer: (bands: number[]) => void
}