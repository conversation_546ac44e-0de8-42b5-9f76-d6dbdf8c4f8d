import { MediaSource, MediaType } from './types'

export function createMediaSource(
  id: string,
  url: string,
  options: {
    type: MediaType
    format: string
    quality?: 'low' | 'medium' | 'high'
    bitrate?: number
    resolution?: string
  }
): MediaSource {
  return {
    id,
    url,
    type: options.type,
    format: options.format,
    quality: options.quality,
    bitrate: options.bitrate,
    resolution: options.resolution,
  }
}

export function getMediaTypeFromMimeType(mimeType: string): MediaType {
  if (mimeType.startsWith('video/')) {
    return MediaType.VIDEO
  } else if (mimeType.startsWith('audio/')) {
    return MediaType.AUDIO
  } else if (mimeType.startsWith('image/')) {
    return MediaType.IMAGE
  }
  
  return MediaType.VIDEO // Default fallback
}

export function formatTime(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '0:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

export function getVideoQualityLabel(resolution: string): string {
  const resolutionMap: Record<string, string> = {
    '3840x2160': '4K',
    '2560x1440': '1440p',
    '1920x1080': '1080p',
    '1280x720': '720p',
    '854x480': '480p',
    '640x360': '360p',
    '426x240': '240p',
  }

  return resolutionMap[resolution] || resolution
}

export function getQualityLabel(quality: string): string {
  const qualityMap: Record<string, string> = {
    'auto': 'Auto',
    'high': 'High',
    'medium': 'Medium',
    'low': 'Low',
    '4k': '4K',
    '1080p': '1080p',
    '720p': '720p',
    '480p': '480p',
    '360p': '360p',
    '240p': '240p'
  }

  return qualityMap[quality.toLowerCase()] || quality
}

export function isVideoFormat(format: string): boolean {
  const videoFormats = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'm4v']
  return videoFormats.includes(format.toLowerCase())
}

export function isAudioFormat(format: string): boolean {
  const audioFormats = ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac', 'wma']
  return audioFormats.includes(format.toLowerCase())
}

export function isImageFormat(format: string): boolean {
  const imageFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp']
  return imageFormats.includes(format.toLowerCase())
}

export function generateThumbnail(
  videoElement: HTMLVideoElement,
  time: number = 0
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Could not get canvas context'))
      return
    }
    
    const seekAndCapture = () => {
      canvas.width = videoElement.videoWidth
      canvas.height = videoElement.videoHeight
      
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
      
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          resolve(url)
        } else {
          reject(new Error('Failed to generate thumbnail'))
        }
      }, 'image/jpeg', 0.8)
      
      videoElement.removeEventListener('seeked', seekAndCapture)
    }
    
    videoElement.addEventListener('seeked', seekAndCapture)
    videoElement.currentTime = time
  })
}

export function preloadMedia(url: string, type: MediaType): Promise<HTMLVideoElement | HTMLAudioElement | HTMLImageElement> {
  return new Promise((resolve, reject) => {
    let element: HTMLVideoElement | HTMLAudioElement | HTMLImageElement
    
    if (type === MediaType.VIDEO) {
      element = document.createElement('video')
      element.preload = 'metadata'
    } else if (type === MediaType.AUDIO) {
      element = document.createElement('audio')
      element.preload = 'metadata'
    } else {
      element = document.createElement('img')
    }
    
    element.addEventListener('loadedmetadata', () => resolve(element))
    element.addEventListener('load', () => resolve(element)) // For images
    element.addEventListener('error', reject)
    
    element.src = url
  })
}