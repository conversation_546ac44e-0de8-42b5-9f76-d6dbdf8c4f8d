import { BaseMediaPlayer } from './base-player'
import { AudioPlayer, PlayerConfig, MediaSource } from './types'

export class HVPPYAudioPlayer extends BaseMediaPlayer implements AudioPlayer {
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  private source: MediaElementAudioSourceNode | null = null
  private gainNode: GainNode | null = null
  private frequencyData: Uint8Array | null = null
  private waveformData: Float32Array | null = null
  private equalizerNodes: BiquadFilterNode[] = []

  constructor(element: HTMLAudioElement, config: Partial<PlayerConfig> = {}) {
    super(element, config)
    this.initializeWebAudio()
  }

  private async initializeWebAudio(): Promise<void> {
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // Create analyser for frequency data
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 2048
      this.analyser.smoothingTimeConstant = 0.8
      
      // Create gain node for volume control
      this.gainNode = this.audioContext.createGain()
      
      // Create source from audio element
      this.source = this.audioContext.createMediaElementSource(this.element as HTMLAudioElement)
      
      // Connect nodes: source -> gain -> analyser -> destination
      this.source.connect(this.gainNode)
      this.gainNode.connect(this.analyser)
      this.analyser.connect(this.audioContext.destination)
      
      // Initialize frequency and waveform data arrays
      this.frequencyData = new Uint8Array(this.analyser.frequencyBinCount)
      this.waveformData = new Float32Array(this.analyser.fftSize)
      
      // Setup equalizer bands (10-band EQ)
      this.setupEqualizer()
      
    } catch (error) {
      console.warn('Web Audio API not supported or failed to initialize:', error)
    }
  }

  private setupEqualizer(): void {
    if (!this.audioContext || !this.gainNode || !this.analyser) return

    // Standard 10-band equalizer frequencies
    const frequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000]
    
    // Clear existing equalizer nodes
    this.equalizerNodes.forEach(node => node.disconnect())
    this.equalizerNodes = []
    
    // Disconnect and reconnect with equalizer
    this.gainNode.disconnect()
    
    let previousNode: AudioNode = this.gainNode
    
    frequencies.forEach((frequency, index) => {
      const filter = this.audioContext!.createBiquadFilter()
      filter.type = index === 0 ? 'lowshelf' : index === frequencies.length - 1 ? 'highshelf' : 'peaking'
      filter.frequency.value = frequency
      filter.Q.value = 1
      filter.gain.value = 0 // Start with flat EQ
      
      previousNode.connect(filter)
      this.equalizerNodes.push(filter)
      previousNode = filter
    })
    
    // Connect last filter to analyser
    previousNode.connect(this.analyser)
  }

  getFrequencyData(): Uint8Array | null {
    if (!this.analyser || !this.frequencyData) return null
    
    this.analyser.getByteFrequencyData(this.frequencyData)
    return this.frequencyData
  }

  getWaveformData(): Float32Array | null {
    if (!this.analyser || !this.waveformData) return null
    
    this.analyser.getFloatTimeDomainData(this.waveformData)
    return this.waveformData
  }

  setEqualizer(bands: number[]): void {
    if (!this.equalizerNodes || bands.length !== this.equalizerNodes.length) {
      console.warn('Invalid equalizer bands array')
      return
    }
    
    bands.forEach((gain, index) => {
      if (this.equalizerNodes[index]) {
        // Clamp gain between -12dB and +12dB
        this.equalizerNodes[index].gain.value = Math.max(-12, Math.min(12, gain))
      }
    })
  }

  getAudioContext(): AudioContext | null {
    return this.audioContext
  }

  setVolume(volume: number): void {
    super.setVolume(volume)
    
    // Also update Web Audio gain node if available
    if (this.gainNode) {
      this.gainNode.gain.value = volume
    }
  }

  async play(): Promise<void> {
    // Resume audio context if suspended (required for autoplay policies)
    if (this.audioContext && this.audioContext.state === 'suspended') {
      await this.audioContext.resume()
    }
    
    return super.play()
  }

  destroy(): void {
    // Clean up Web Audio nodes
    if (this.source) {
      this.source.disconnect()
      this.source = null
    }
    
    if (this.gainNode) {
      this.gainNode.disconnect()
      this.gainNode = null
    }
    
    if (this.analyser) {
      this.analyser.disconnect()
      this.analyser = null
    }
    
    this.equalizerNodes.forEach(node => node.disconnect())
    this.equalizerNodes = []
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }
    
    this.frequencyData = null
    this.waveformData = null
    
    super.destroy()
  }
}

// Factory function to create audio player
export function createAudioPlayer(
  element: HTMLAudioElement, 
  config: Partial<PlayerConfig> = {}
): AudioPlayer {
  return new HVPPYAudioPlayer(element, config)
}

// Utility functions
export function isAudioSupported(): boolean {
  const audio = document.createElement('audio')
  return !!(audio.canPlayType)
}

export function getSupportedAudioFormats(): string[] {
  const audio = document.createElement('audio')
  const formats: string[] = []
  
  const testFormats = [
    { format: 'mp3', mime: 'audio/mpeg' },
    { format: 'wav', mime: 'audio/wav' },
    { format: 'ogg', mime: 'audio/ogg; codecs="vorbis"' },
    { format: 'aac', mime: 'audio/aac' },
    { format: 'm4a', mime: 'audio/mp4; codecs="mp4a.40.2"' },
    { format: 'flac', mime: 'audio/flac' },
    { format: 'webm', mime: 'audio/webm; codecs="vorbis"' }
  ]
  
  testFormats.forEach(({ format, mime }) => {
    if (audio.canPlayType(mime) !== '') {
      formats.push(format)
    }
  })
  
  return formats
}

export function isWebAudioSupported(): boolean {
  return !!(window.AudioContext || (window as any).webkitAudioContext)
}
