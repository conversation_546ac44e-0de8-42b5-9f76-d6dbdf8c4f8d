import { BaseMediaPlayer } from './base-player'
import { VideoPlayer, PlayerConfig, PlaybackQuality } from './types'

export class HVPPYVideoPlayer extends BaseMediaPlayer implements VideoPlayer {
  private availableQualities: PlaybackQuality[] = ['auto', 'high', 'medium', 'low']
  private currentQuality: PlaybackQuality = 'auto'
  private fullscreenElement: HTMLElement | null = null

  constructor(element: HTMLVideoElement, config: Partial<PlayerConfig> = {}) {
    super(element, config)
    this.setupVideoSpecificListeners()
  }

  private setupVideoSpecificListeners(): void {
    // Handle fullscreen changes
    document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this))
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this))
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this))
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this))
  }

  private handleFullscreenChange(): void {
    const isFullscreen = this.isFullscreen()
    this.emit('onFullscreenChange', isFullscreen)
  }

  setQuality(quality: PlaybackQuality): void {
    if (!this.availableQualities.includes(quality)) {
      console.warn(`Quality ${quality} not available`)
      return
    }

    const currentTime = this.element.currentTime
    const wasPlaying = this.isPlaying()
    
    this.currentQuality = quality
    
    // In a real implementation, you would switch to a different source URL
    // For now, we'll just emit an event
    this.emit('onQualityChange', quality)
    
    // Restore playback state
    if (currentTime > 0) {
      this.element.currentTime = currentTime
    }
    
    if (wasPlaying) {
      this.play()
    }
  }

  getAvailableQualities(): PlaybackQuality[] {
    return [...this.availableQualities]
  }

  async enterFullscreen(): Promise<void> {
    const videoElement = this.element as HTMLVideoElement
    
    try {
      if (videoElement.requestFullscreen) {
        await videoElement.requestFullscreen()
      } else if ((videoElement as any).webkitRequestFullscreen) {
        await (videoElement as any).webkitRequestFullscreen()
      } else if ((videoElement as any).mozRequestFullScreen) {
        await (videoElement as any).mozRequestFullScreen()
      } else if ((videoElement as any).msRequestFullscreen) {
        await (videoElement as any).msRequestFullscreen()
      } else {
        throw new Error('Fullscreen not supported')
      }
      
      this.fullscreenElement = videoElement
    } catch (error) {
      console.error('Failed to enter fullscreen:', error)
      throw error
    }
  }

  async exitFullscreen(): Promise<void> {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen()
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen()
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen()
      }
      
      this.fullscreenElement = null
    } catch (error) {
      console.error('Failed to exit fullscreen:', error)
      throw error
    }
  }

  isFullscreen(): boolean {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    )
  }

  setSubtitles(enabled: boolean): void {
    const videoElement = this.element as HTMLVideoElement
    const tracks = videoElement.textTracks
    
    for (let i = 0; i < tracks.length; i++) {
      const track = tracks[i]
      if (track.kind === 'subtitles' || track.kind === 'captions') {
        track.mode = enabled ? 'showing' : 'hidden'
      }
    }
    
    this.emit('onSubtitlesChange', enabled)
  }

  // Override destroy to clean up video-specific listeners
  destroy(): void {
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange.bind(this))
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this))
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this))
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this))
    
    if (this.isFullscreen()) {
      this.exitFullscreen().catch(console.error)
    }
    
    super.destroy()
  }
}

// Factory function to create video player
export function createVideoPlayer(
  element: HTMLVideoElement, 
  config: Partial<PlayerConfig> = {}
): VideoPlayer {
  return new HVPPYVideoPlayer(element, config)
}

// Utility functions
export function isVideoSupported(): boolean {
  const video = document.createElement('video')
  return !!(video.canPlayType)
}

export function getSupportedVideoFormats(): string[] {
  const video = document.createElement('video')
  const formats: string[] = []
  
  const testFormats = [
    { format: 'mp4', mime: 'video/mp4; codecs="avc1.42E01E"' },
    { format: 'webm', mime: 'video/webm; codecs="vp8, vorbis"' },
    { format: 'webm', mime: 'video/webm; codecs="vp9"' },
    { format: 'ogg', mime: 'video/ogg; codecs="theora"' },
    { format: 'mov', mime: 'video/quicktime' },
    { format: 'avi', mime: 'video/x-msvideo' }
  ]
  
  testFormats.forEach(({ format, mime }) => {
    if (video.canPlayType(mime) !== '') {
      formats.push(format)
    }
  })
  
  return formats
}

export function getOptimalVideoQuality(
  availableQualities: PlaybackQuality[],
  bandwidth?: number,
  devicePixelRatio?: number
): PlaybackQuality {
  // Simple quality selection logic
  const dpr = devicePixelRatio || window.devicePixelRatio || 1

  if (bandwidth) {
    // Bandwidth-based selection (simplified)
    if (bandwidth > 5000000 && availableQualities.includes('high')) return 'high' // 5 Mbps+
    if (bandwidth > 2000000 && availableQualities.includes('medium')) return 'medium' // 2 Mbps+
    if (availableQualities.includes('low')) return 'low'
  }

  // Device-based selection
  if (dpr >= 2 && availableQualities.includes('high')) return 'high'
  if (dpr >= 1.5 && availableQualities.includes('medium')) return 'medium'
  if (availableQualities.includes('low')) return 'low'

  // Fallback to first available quality
  return availableQualities[0] || 'auto'
}

// Picture-in-Picture support
export function isPictureInPictureSupported(): boolean {
  return 'pictureInPictureEnabled' in document
}

export async function enterPictureInPicture(videoElement: HTMLVideoElement): Promise<void> {
  if (!isPictureInPictureSupported()) {
    throw new Error('Picture-in-Picture not supported')
  }
  
  try {
    await (videoElement as any).requestPictureInPicture()
  } catch (error) {
    console.error('Failed to enter Picture-in-Picture:', error)
    throw error
  }
}

export async function exitPictureInPicture(): Promise<void> {
  if (!isPictureInPictureSupported()) {
    throw new Error('Picture-in-Picture not supported')
  }
  
  try {
    await (document as any).exitPictureInPicture()
  } catch (error) {
    console.error('Failed to exit Picture-in-Picture:', error)
    throw error
  }
}
