// HVPPY Central Media Player System - Main Export File

// Core types and interfaces
export * from './types'

// Base player implementation
export { BaseMediaPlayer } from './base-player'

// Specific player implementations
export { 
  HVPPYVideoPlayer, 
  createVideoPlayer, 
  isVideoSupported, 
  getSupportedVideoFormats,
  getOptimalVideoQuality,
  isPictureInPictureSupported,
  enterPictureInPicture,
  exitPictureInPicture
} from './video-player'

export { 
  HVPPYAudioPlayer, 
  createAudioPlayer, 
  isAudioSupported, 
  getSupportedAudioFormats,
  isWebAudioSupported 
} from './audio-player'

// Utility functions
export * from './utils'

// Player manager for global state management
class PlayerManager {
  private players: Map<string, BaseMediaPlayer> = new Map()
  private activePlayer: BaseMediaPlayer | null = null

  register(id: string, player: BaseMediaPlayer): void {
    this.players.set(id, player)
  }

  unregister(id: string): void {
    const player = this.players.get(id)
    if (player) {
      player.destroy()
      this.players.delete(id)
      
      if (this.activePlayer === player) {
        this.activePlayer = null
      }
    }
  }

  setActive(id: string): void {
    const player = this.players.get(id)
    if (player) {
      // Pause other players
      this.players.forEach((p, playerId) => {
        if (playerId !== id && p.isPlaying()) {
          p.pause()
        }
      })
      
      this.activePlayer = player
    }
  }

  getActive(): BaseMediaPlayer | null {
    return this.activePlayer
  }

  pauseAll(): void {
    this.players.forEach(player => {
      if (player.isPlaying()) {
        player.pause()
      }
    })
  }

  stopAll(): void {
    this.players.forEach(player => {
      player.stop()
    })
  }

  destroyAll(): void {
    this.players.forEach(player => {
      player.destroy()
    })
    this.players.clear()
    this.activePlayer = null
  }

  getPlayerCount(): number {
    return this.players.size
  }

  getPlayers(): BaseMediaPlayer[] {
    return Array.from(this.players.values())
  }
}

// Global player manager instance
export const globalPlayerManager = new PlayerManager()

// Convenience factory functions
export function createPlayer(
  element: HTMLMediaElement,
  config: Partial<import('./types').PlayerConfig> = {}
): BaseMediaPlayer {
  if (element instanceof HTMLVideoElement) {
    return createVideoPlayer(element, config)
  } else if (element instanceof HTMLAudioElement) {
    return createAudioPlayer(element, config)
  } else {
    throw new Error('Unsupported media element type')
  }
}

// Auto-register players with global manager
export function createManagedPlayer(
  id: string,
  element: HTMLMediaElement,
  config: Partial<import('./types').PlayerConfig> = {}
): BaseMediaPlayer {
  const player = createPlayer(element, config)
  globalPlayerManager.register(id, player)
  return player
}

// Quality detection utilities
export function detectOptimalQuality(): import('./types').PlaybackQuality {
  // Simple quality detection based on connection and device
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
  
  if (connection) {
    const effectiveType = connection.effectiveType
    switch (effectiveType) {
      case '4g':
        return 'high'
      case '3g':
        return 'medium'
      case '2g':
      case 'slow-2g':
        return 'low'
      default:
        return 'auto'
    }
  }
  
  // Fallback to device pixel ratio
  const dpr = window.devicePixelRatio || 1
  if (dpr >= 2) return 'high'
  if (dpr >= 1.5) return 'medium'
  return 'low'
}

// Media format detection
export function getPreferredFormat(type: 'video' | 'audio'): string {
  if (type === 'video') {
    const formats = getSupportedVideoFormats()
    // Prefer modern formats
    if (formats.includes('webm')) return 'webm'
    if (formats.includes('mp4')) return 'mp4'
    return formats[0] || 'mp4'
  } else {
    const formats = getSupportedAudioFormats()
    // Prefer modern formats
    if (formats.includes('webm')) return 'webm'
    if (formats.includes('mp3')) return 'mp3'
    return formats[0] || 'mp3'
  }
}

// Browser capability detection
export function getPlayerCapabilities() {
  return {
    video: {
      supported: isVideoSupported(),
      formats: getSupportedVideoFormats(),
      pictureInPicture: isPictureInPictureSupported(),
      fullscreen: !!(
        document.fullscreenEnabled ||
        (document as any).webkitFullscreenEnabled ||
        (document as any).mozFullScreenEnabled ||
        (document as any).msFullscreenEnabled
      )
    },
    audio: {
      supported: isAudioSupported(),
      formats: getSupportedAudioFormats(),
      webAudio: isWebAudioSupported()
    }
  }
}

// Version information
export const PLAYER_VERSION = '1.0.0'
export const PLAYER_NAME = 'HVPPY Central Media Player'

/**
 * Initialize the player system
 */
export function initializePlayerSystem(): void {
  console.log(`${PLAYER_NAME} v${PLAYER_VERSION} initialized`)
  
  // Setup global error handling
  window.addEventListener('error', (event) => {
    if (event.target instanceof HTMLMediaElement) {
      console.error('Media element error:', event.target.error)
    }
  })
  
  // Setup visibility change handling to pause players when tab is hidden
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      globalPlayerManager.pauseAll()
    }
  })
  
  // Setup beforeunload to clean up players
  window.addEventListener('beforeunload', () => {
    globalPlayerManager.destroyAll()
  })
}

// Auto-initialize on import
if (typeof window !== 'undefined') {
  // Delay initialization to ensure DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePlayerSystem)
  } else {
    initializePlayerSystem()
  }
}
