import { create } from 'zustand'
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware'
import { InteractionType, InteractionData } from '@/types/feed'

interface RealtimeInteraction {
  id: string
  postId: string
  userId: string
  type: InteractionType
  mood?: string
  timestamp: Date
  optimistic: boolean
  synced: boolean
}

interface InteractionMetrics {
  totalLikes: number
  totalShares: number
  totalComments: number
  totalViews: number
  engagementRate: number
  averageViewTime: number
}

interface InteractionState {
  // Real-time interactions
  interactions: Map<string, RealtimeInteraction>
  pendingInteractions: Map<string, RealtimeInteraction>
  
  // User interaction history
  userInteractions: Map<string, InteractionData[]>
  recentInteractions: RealtimeInteraction[]
  
  // Metrics and analytics
  metrics: InteractionMetrics
  postMetrics: Map<string, InteractionMetrics>
  
  // Social features
  following: Set<string>
  followers: Set<string>
  blockedUsers: Set<string>
  
  // Real-time state
  isConnected: boolean
  lastSync: Date | null
  syncQueue: RealtimeInteraction[]
  
  // Actions
  addInteraction: (interaction: Omit<RealtimeInteraction, 'id' | 'timestamp'>) => Promise<void>
  removeInteraction: (interactionId: string) => Promise<void>
  toggleLike: (postId: string, mood?: string) => Promise<void>
  toggleFollow: (userId: string) => Promise<void>
  sharePost: (postId: string, platform?: string) => Promise<void>
  addComment: (postId: string, content: string, mood?: string) => Promise<void>
  recordView: (postId: string, duration: number) => void
  
  // Real-time sync
  syncInteractions: () => Promise<void>
  handleRealtimeUpdate: (interaction: RealtimeInteraction) => void
  
  // Metrics
  updateMetrics: (postId: string, metrics: Partial<InteractionMetrics>) => void
  getEngagementScore: (postId: string) => number
  
  // Social actions
  followUser: (userId: string) => Promise<void>
  unfollowUser: (userId: string) => Promise<void>
  blockUser: (userId: string) => Promise<void>
  unblockUser: (userId: string) => Promise<void>
  
  // Utilities
  getPostInteractions: (postId: string) => RealtimeInteraction[]
  getUserInteractionHistory: (userId: string) => InteractionData[]
  clearPendingInteractions: () => void
  reset: () => void
}

const defaultMetrics: InteractionMetrics = {
  totalLikes: 0,
  totalShares: 0,
  totalComments: 0,
  totalViews: 0,
  engagementRate: 0,
  averageViewTime: 0,
}

export const useEnhancedInteractionStore = create<InteractionState>()(
  devtools(
    subscribeWithSelector(
      persist(
        (set, get) => ({
          // Initial state
          interactions: new Map(),
          pendingInteractions: new Map(),
          userInteractions: new Map(),
          recentInteractions: [],
          metrics: defaultMetrics,
          postMetrics: new Map(),
          following: new Set(),
          followers: new Set(),
          blockedUsers: new Set(),
          isConnected: false,
          lastSync: null,
          syncQueue: [],

          // Enhanced interaction actions
          addInteraction: async (interactionData) => {
            const interaction: RealtimeInteraction = {
              ...interactionData,
              id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              timestamp: new Date(),
              optimistic: true,
              synced: false,
            }

            // Add to pending interactions for optimistic updates
            set((state) => {
              const newPending = new Map(state.pendingInteractions)
              newPending.set(interaction.id, interaction)
              
              const newRecent = [interaction, ...state.recentInteractions.slice(0, 49)]
              
              return {
                pendingInteractions: newPending,
                recentInteractions: newRecent,
                syncQueue: [...state.syncQueue, interaction]
              }
            })

            // Attempt to sync immediately
            try {
              await get().syncInteractions()
            } catch (error) {
              console.warn('Failed to sync interaction immediately:', error)
            }
          },

          removeInteraction: async (interactionId: string) => {
            set((state) => {
              const newInteractions = new Map(state.interactions)
              const newPending = new Map(state.pendingInteractions)
              
              newInteractions.delete(interactionId)
              newPending.delete(interactionId)
              
              return {
                interactions: newInteractions,
                pendingInteractions: newPending,
                recentInteractions: state.recentInteractions.filter(i => i.id !== interactionId)
              }
            })
          },

          toggleLike: async (postId: string, mood?: string) => {
            const { interactions, pendingInteractions } = get()
            
            // Check if already liked
            const existingLike = Array.from(interactions.values())
              .concat(Array.from(pendingInteractions.values()))
              .find(i => i.postId === postId && i.type === InteractionType.LIKE)

            if (existingLike) {
              await get().removeInteraction(existingLike.id)
            } else {
              await get().addInteraction({
                postId,
                userId: 'current-user', // TODO: Get from auth store
                type: InteractionType.LIKE,
                mood,
                optimistic: true,
                synced: false,
              })
            }
          },

          toggleFollow: async (userId: string) => {
            const { following } = get()
            
            if (following.has(userId)) {
              await get().unfollowUser(userId)
            } else {
              await get().followUser(userId)
            }
          },

          sharePost: async (postId: string, platform?: string) => {
            await get().addInteraction({
              postId,
              userId: 'current-user', // TODO: Get from auth store
              type: InteractionType.SHARE,
              optimistic: true,
              synced: false,
            })

            // Update metrics optimistically
            get().updateMetrics(postId, {
              totalShares: (get().postMetrics.get(postId)?.totalShares || 0) + 1
            })
          },

          addComment: async (postId: string, content: string, mood?: string) => {
            await get().addInteraction({
              postId,
              userId: 'current-user', // TODO: Get from auth store
              type: InteractionType.COMMENT,
              mood,
              optimistic: true,
              synced: false,
            })

            // Update metrics optimistically
            get().updateMetrics(postId, {
              totalComments: (get().postMetrics.get(postId)?.totalComments || 0) + 1
            })
          },

          recordView: (postId: string, duration: number) => {
            // Record view interaction
            get().addInteraction({
              postId,
              userId: 'current-user', // TODO: Get from auth store
              type: InteractionType.VIEW,
              optimistic: false,
              synced: false,
            })

            // Update view metrics
            const currentMetrics = get().postMetrics.get(postId) || defaultMetrics
            get().updateMetrics(postId, {
              totalViews: currentMetrics.totalViews + 1,
              averageViewTime: (currentMetrics.averageViewTime + duration) / 2
            })
          },

          // Real-time sync implementation
          syncInteractions: async () => {
            const { syncQueue, isConnected } = get()
            
            if (!isConnected || syncQueue.length === 0) {
              return
            }

            try {
              // TODO: Implement actual API sync
              const response = await fetch('/api/interactions/sync', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ interactions: syncQueue })
              })

              if (response.ok) {
                const syncedInteractions = await response.json()
                
                set((state) => {
                  const newInteractions = new Map(state.interactions)
                  const newPending = new Map(state.pendingInteractions)
                  
                  syncedInteractions.forEach((interaction: RealtimeInteraction) => {
                    interaction.synced = true
                    interaction.optimistic = false
                    newInteractions.set(interaction.id, interaction)
                    newPending.delete(interaction.id)
                  })
                  
                  return {
                    interactions: newInteractions,
                    pendingInteractions: newPending,
                    syncQueue: [],
                    lastSync: new Date(),
                    isConnected: true
                  }
                })
              }
            } catch (error) {
              console.error('Failed to sync interactions:', error)
              set({ isConnected: false })
            }
          },

          handleRealtimeUpdate: (interaction: RealtimeInteraction) => {
            set((state) => {
              const newInteractions = new Map(state.interactions)
              newInteractions.set(interaction.id, interaction)
              
              const newRecent = [interaction, ...state.recentInteractions.slice(0, 49)]
              
              return {
                interactions: newInteractions,
                recentInteractions: newRecent
              }
            })
          },

          // Metrics and analytics
          updateMetrics: (postId: string, metrics: Partial<InteractionMetrics>) => {
            set((state) => {
              const newPostMetrics = new Map(state.postMetrics)
              const currentMetrics = newPostMetrics.get(postId) || defaultMetrics
              newPostMetrics.set(postId, { ...currentMetrics, ...metrics })
              
              return { postMetrics: newPostMetrics }
            })
          },

          getEngagementScore: (postId: string) => {
            const metrics = get().postMetrics.get(postId)
            if (!metrics) return 0
            
            const { totalLikes, totalShares, totalComments, totalViews } = metrics
            const engagementActions = totalLikes + totalShares + totalComments
            
            return totalViews > 0 ? (engagementActions / totalViews) * 100 : 0
          },

          // Social actions
          followUser: async (userId: string) => {
            set((state) => ({
              following: new Set([...state.following, userId])
            }))
            
            // TODO: Sync with backend
            try {
              await fetch(`/api/users/${userId}/follow`, { method: 'POST' })
            } catch (error) {
              console.error('Failed to follow user:', error)
              // Rollback on error
              set((state) => {
                const newFollowing = new Set(state.following)
                newFollowing.delete(userId)
                return { following: newFollowing }
              })
            }
          },

          unfollowUser: async (userId: string) => {
            set((state) => {
              const newFollowing = new Set(state.following)
              newFollowing.delete(userId)
              return { following: newFollowing }
            })
            
            // TODO: Sync with backend
            try {
              await fetch(`/api/users/${userId}/unfollow`, { method: 'POST' })
            } catch (error) {
              console.error('Failed to unfollow user:', error)
              // Rollback on error
              set((state) => ({
                following: new Set([...state.following, userId])
              }))
            }
          },

          blockUser: async (userId: string) => {
            set((state) => ({
              blockedUsers: new Set([...state.blockedUsers, userId])
            }))
            
            // TODO: Sync with backend
          },

          unblockUser: async (userId: string) => {
            set((state) => {
              const newBlocked = new Set(state.blockedUsers)
              newBlocked.delete(userId)
              return { blockedUsers: newBlocked }
            })
            
            // TODO: Sync with backend
          },

          // Utility functions
          getPostInteractions: (postId: string) => {
            const { interactions, pendingInteractions } = get()
            return Array.from(interactions.values())
              .concat(Array.from(pendingInteractions.values()))
              .filter(i => i.postId === postId)
          },

          getUserInteractionHistory: (userId: string) => {
            const { userInteractions } = get()
            return userInteractions.get(userId) || []
          },

          clearPendingInteractions: () => {
            set({ pendingInteractions: new Map(), syncQueue: [] })
          },

          reset: () => {
            set({
              interactions: new Map(),
              pendingInteractions: new Map(),
              userInteractions: new Map(),
              recentInteractions: [],
              metrics: defaultMetrics,
              postMetrics: new Map(),
              following: new Set(),
              followers: new Set(),
              blockedUsers: new Set(),
              isConnected: false,
              lastSync: null,
              syncQueue: [],
            })
          },
        }),
        {
          name: 'hvppy-enhanced-interaction-store',
          partialize: (state) => ({
            following: Array.from(state.following),
            blockedUsers: Array.from(state.blockedUsers),
            userInteractions: Object.fromEntries(state.userInteractions),
          }),
        }
      )
    ),
    {
      name: 'hvppy-enhanced-interaction-store',
    }
  )
)

// Enhanced selectors
export const selectInteractions = (state: InteractionState) => state.interactions
export const selectPendingInteractions = (state: InteractionState) => state.pendingInteractions
export const selectRecentInteractions = (state: InteractionState) => state.recentInteractions
export const selectPostMetrics = (postId: string) => (state: InteractionState) => 
  state.postMetrics.get(postId) || defaultMetrics
export const selectIsFollowing = (userId: string) => (state: InteractionState) => 
  state.following.has(userId)
export const selectIsBlocked = (userId: string) => (state: InteractionState) => 
  state.blockedUsers.has(userId)
export const selectSyncStatus = (state: InteractionState) => ({
  isConnected: state.isConnected,
  lastSync: state.lastSync,
  pendingCount: state.syncQueue.length
})
