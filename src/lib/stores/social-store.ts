import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

interface UserProfile {
  id: string
  username: string
  displayName: string
  bio?: string
  avatarUrl?: string
  coverImageUrl?: string
  isVerified: boolean
  followerCount: number
  followingCount: number
  postCount: number
  isFollowing?: boolean
  isFollowedBy?: boolean
  createdAt: Date
}

interface Notification {
  id: string
  type: 'like' | 'comment' | 'follow' | 'mention' | 'share' | 'memory' | 'mood_match'
  fromUser: UserProfile
  postId?: string
  message: string
  isRead: boolean
  createdAt: Date
  metadata?: Record<string, any>
}

interface SocialConnection {
  userId: string
  type: 'follower' | 'following' | 'mutual'
  since: Date
}

interface SocialState {
  // Current user profile
  currentUser: UserProfile | null
  
  // Social connections
  followers: Map<string, UserProfile>
  following: Map<string, UserProfile>
  mutualConnections: Map<string, UserProfile>
  
  // Notifications
  notifications: Notification[]
  unreadCount: number
  
  // User discovery
  suggestedUsers: UserProfile[]
  recentlyViewed: UserProfile[]
  
  // Loading states
  loadingProfile: boolean
  loadingFollowers: boolean
  loadingFollowing: boolean
  loadingNotifications: boolean
  loadingSuggestions: boolean
  
  // Actions - Profile
  setCurrentUser: (user: UserProfile) => void
  updateProfile: (updates: Partial<UserProfile>) => void
  
  // Actions - Social connections
  addFollower: (user: UserProfile) => void
  removeFollower: (userId: string) => void
  addFollowing: (user: UserProfile) => void
  removeFollowing: (userId: string) => void
  loadFollowers: (users: UserProfile[]) => void
  loadFollowing: (users: UserProfile[]) => void
  
  // Actions - Notifications
  addNotification: (notification: Notification) => void
  markNotificationRead: (notificationId: string) => void
  markAllNotificationsRead: () => void
  removeNotification: (notificationId: string) => void
  loadNotifications: (notifications: Notification[]) => void
  
  // Actions - User discovery
  setSuggestedUsers: (users: UserProfile[]) => void
  addRecentlyViewed: (user: UserProfile) => void
  
  // Actions - Loading states
  setLoadingProfile: (loading: boolean) => void
  setLoadingFollowers: (loading: boolean) => void
  setLoadingFollowing: (loading: boolean) => void
  setLoadingNotifications: (loading: boolean) => void
  setLoadingSuggestions: (loading: boolean) => void
  
  // Utility functions
  isFollowing: (userId: string) => boolean
  isFollower: (userId: string) => boolean
  isMutualConnection: (userId: string) => boolean
  getConnectionType: (userId: string) => 'follower' | 'following' | 'mutual' | 'none'
  
  // Analytics
  getEngagementStats: () => {
    totalNotifications: number
    likesReceived: number
    commentsReceived: number
    sharesReceived: number
    newFollowers: number
  }
  
  // Reset
  reset: () => void
}

export const useSocialStore = create<SocialState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentUser: null,
        followers: new Map(),
        following: new Map(),
        mutualConnections: new Map(),
        notifications: [],
        unreadCount: 0,
        suggestedUsers: [],
        recentlyViewed: [],
        loadingProfile: false,
        loadingFollowers: false,
        loadingFollowing: false,
        loadingNotifications: false,
        loadingSuggestions: false,

        // Profile actions
        setCurrentUser: (user: UserProfile) => {
          set({ currentUser: user })
        },

        updateProfile: (updates: Partial<UserProfile>) => {
          set((state) => ({
            currentUser: state.currentUser 
              ? { ...state.currentUser, ...updates }
              : null
          }))
        },

        // Social connection actions
        addFollower: (user: UserProfile) => {
          set((state) => {
            const newFollowers = new Map(state.followers)
            newFollowers.set(user.id, user)
            
            // Update mutual connections if user is also following
            const newMutual = new Map(state.mutualConnections)
            if (state.following.has(user.id)) {
              newMutual.set(user.id, user)
            }
            
            return { 
              followers: newFollowers,
              mutualConnections: newMutual,
            }
          })
        },

        removeFollower: (userId: string) => {
          set((state) => {
            const newFollowers = new Map(state.followers)
            newFollowers.delete(userId)
            
            const newMutual = new Map(state.mutualConnections)
            newMutual.delete(userId)
            
            return { 
              followers: newFollowers,
              mutualConnections: newMutual,
            }
          })
        },

        addFollowing: (user: UserProfile) => {
          set((state) => {
            const newFollowing = new Map(state.following)
            newFollowing.set(user.id, user)
            
            // Update mutual connections if user is also a follower
            const newMutual = new Map(state.mutualConnections)
            if (state.followers.has(user.id)) {
              newMutual.set(user.id, user)
            }
            
            return { 
              following: newFollowing,
              mutualConnections: newMutual,
            }
          })
        },

        removeFollowing: (userId: string) => {
          set((state) => {
            const newFollowing = new Map(state.following)
            newFollowing.delete(userId)
            
            const newMutual = new Map(state.mutualConnections)
            newMutual.delete(userId)
            
            return { 
              following: newFollowing,
              mutualConnections: newMutual,
            }
          })
        },

        loadFollowers: (users: UserProfile[]) => {
          const followersMap = new Map(users.map(user => [user.id, user]))
          set({ followers: followersMap })
        },

        loadFollowing: (users: UserProfile[]) => {
          const followingMap = new Map(users.map(user => [user.id, user]))
          set({ following: followingMap })
        },

        // Notification actions
        addNotification: (notification: Notification) => {
          set((state) => ({
            notifications: [notification, ...state.notifications],
            unreadCount: state.unreadCount + 1,
          }))
        },

        markNotificationRead: (notificationId: string) => {
          set((state) => {
            const updatedNotifications = state.notifications.map(notif =>
              notif.id === notificationId ? { ...notif, isRead: true } : notif
            )
            const unreadCount = updatedNotifications.filter(n => !n.isRead).length
            
            return {
              notifications: updatedNotifications,
              unreadCount,
            }
          })
        },

        markAllNotificationsRead: () => {
          set((state) => ({
            notifications: state.notifications.map(notif => ({ ...notif, isRead: true })),
            unreadCount: 0,
          }))
        },

        removeNotification: (notificationId: string) => {
          set((state) => {
            const updatedNotifications = state.notifications.filter(n => n.id !== notificationId)
            const unreadCount = updatedNotifications.filter(n => !n.isRead).length
            
            return {
              notifications: updatedNotifications,
              unreadCount,
            }
          })
        },

        loadNotifications: (notifications: Notification[]) => {
          const unreadCount = notifications.filter(n => !n.isRead).length
          set({ notifications, unreadCount })
        },

        // User discovery actions
        setSuggestedUsers: (users: UserProfile[]) => {
          set({ suggestedUsers: users })
        },

        addRecentlyViewed: (user: UserProfile) => {
          set((state) => {
            const filtered = state.recentlyViewed.filter(u => u.id !== user.id)
            return {
              recentlyViewed: [user, ...filtered].slice(0, 20), // Keep last 20
            }
          })
        },

        // Loading state actions
        setLoadingProfile: (loading: boolean) => set({ loadingProfile: loading }),
        setLoadingFollowers: (loading: boolean) => set({ loadingFollowers: loading }),
        setLoadingFollowing: (loading: boolean) => set({ loadingFollowing: loading }),
        setLoadingNotifications: (loading: boolean) => set({ loadingNotifications: loading }),
        setLoadingSuggestions: (loading: boolean) => set({ loadingSuggestions: loading }),

        // Utility functions
        isFollowing: (userId: string) => {
          return get().following.has(userId)
        },

        isFollower: (userId: string) => {
          return get().followers.has(userId)
        },

        isMutualConnection: (userId: string) => {
          return get().mutualConnections.has(userId)
        },

        getConnectionType: (userId: string) => {
          const { following, followers } = get()
          const isFollowingUser = following.has(userId)
          const isFollowerUser = followers.has(userId)
          
          if (isFollowingUser && isFollowerUser) return 'mutual'
          if (isFollowingUser) return 'following'
          if (isFollowerUser) return 'follower'
          return 'none'
        },

        // Analytics
        getEngagementStats: () => {
          const { notifications } = get()
          const stats = {
            totalNotifications: notifications.length,
            likesReceived: 0,
            commentsReceived: 0,
            sharesReceived: 0,
            newFollowers: 0,
          }
          
          notifications.forEach(notif => {
            switch (notif.type) {
              case 'like':
                stats.likesReceived++
                break
              case 'comment':
                stats.commentsReceived++
                break
              case 'share':
                stats.sharesReceived++
                break
              case 'follow':
                stats.newFollowers++
                break
            }
          })
          
          return stats
        },

        // Reset
        reset: () => {
          set({
            currentUser: null,
            followers: new Map(),
            following: new Map(),
            mutualConnections: new Map(),
            notifications: [],
            unreadCount: 0,
            suggestedUsers: [],
            recentlyViewed: [],
            loadingProfile: false,
            loadingFollowers: false,
            loadingFollowing: false,
            loadingNotifications: false,
            loadingSuggestions: false,
          })
        },
      }),
      {
        name: 'hvppy-social-store',
        partialize: (state) => ({
          currentUser: state.currentUser,
          recentlyViewed: state.recentlyViewed,
        }),
      }
    ),
    {
      name: 'hvppy-social-store',
    }
  )
)

// Selectors for optimized re-renders
export const selectCurrentUser = (state: SocialState) => state.currentUser
export const selectFollowers = (state: SocialState) => Array.from(state.followers.values())
export const selectFollowing = (state: SocialState) => Array.from(state.following.values())
export const selectNotifications = (state: SocialState) => state.notifications
export const selectUnreadCount = (state: SocialState) => state.unreadCount
export const selectSuggestedUsers = (state: SocialState) => state.suggestedUsers
export const selectRecentlyViewed = (state: SocialState) => state.recentlyViewed
export const selectIsFollowing = (userId: string) => (state: SocialState) => state.following.has(userId)
export const selectIsFollower = (userId: string) => (state: SocialState) => state.followers.has(userId)
export const selectConnectionType = (userId: string) => (state: SocialState) => {
  const isFollowingUser = state.following.has(userId)
  const isFollowerUser = state.followers.has(userId)

  if (isFollowingUser && isFollowerUser) return 'mutual'
  if (isFollowingUser) return 'following'
  if (isFollowerUser) return 'follower'
  return 'none'
}
