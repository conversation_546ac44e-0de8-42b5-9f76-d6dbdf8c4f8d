import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { InteractionType, ReactionType } from '@/types'

interface UserInteraction {
  postId: string
  type: InteractionType
  reactionType?: ReactionType
  timestamp: Date
  metadata?: Record<string, any>
}

interface InteractionState {
  // User interactions
  reactions: Map<string, ReactionType>
  memories: Set<string>
  shares: Set<string>
  saves: Set<string>
  views: Map<string, number>
  watchTime: Map<string, number>
  
  // Loading states
  loadingReactions: Set<string>
  loadingMemories: Set<string>
  loadingShares: Set<string>
  loadingSaves: Set<string>
  
  // Interaction history
  interactionHistory: UserInteraction[]
  
  // Actions
  setReaction: (postId: string, reaction: ReactionType) => void
  removeReaction: (postId: string) => void
  addMemory: (postId: string) => void
  removeMemory: (postId: string) => void
  addShare: (postId: string) => void
  addSave: (postId: string) => void
  removeSave: (postId: string) => void
  incrementView: (postId: string) => void
  updateWatchTime: (postId: string, time: number) => void
  
  // Loading state management
  setReactionLoading: (postId: string, loading: boolean) => void
  setMemoryLoading: (postId: string, loading: boolean) => void
  setShareLoading: (postId: string, loading: boolean) => void
  setSaveLoading: (postId: string, loading: boolean) => void
  
  // Bulk operations
  loadUserInteractions: (interactions: UserInteraction[]) => void
  clearInteractions: () => void
  
  // Analytics
  getEngagementScore: (postId: string) => number
  getMostEngagedPosts: (limit?: number) => string[]
}

export const useInteractionStore = create<InteractionState>()(
  devtools(
    (set, get) => ({
      // Initial state
      reactions: new Map(),
      memories: new Set(),
      shares: new Set(),
      saves: new Set(),
      views: new Map(),
      watchTime: new Map(),
      loadingReactions: new Set(),
      loadingMemories: new Set(),
      loadingShares: new Set(),
      loadingSaves: new Set(),
      interactionHistory: [],

      // Reaction management
      setReaction: (postId: string, reaction: ReactionType) => {
        set((state) => {
          const newReactions = new Map(state.reactions)
          newReactions.set(postId, reaction)
          
          const newHistory = [...state.interactionHistory, {
            postId,
            type: InteractionType.LIKE,
            reactionType: reaction,
            timestamp: new Date(),
          }]
          
          return { 
            reactions: newReactions,
            interactionHistory: newHistory,
          }
        })
      },

      removeReaction: (postId: string) => {
        set((state) => {
          const newReactions = new Map(state.reactions)
          newReactions.delete(postId)
          return { reactions: newReactions }
        })
      },

      // Memory management
      addMemory: (postId: string) => {
        set((state) => {
          const newMemories = new Set(state.memories)
          newMemories.add(postId)
          
          const newHistory = [...state.interactionHistory, {
            postId,
            type: InteractionType.MEMORY,
            timestamp: new Date(),
          }]
          
          return { 
            memories: newMemories,
            interactionHistory: newHistory,
          }
        })
      },

      removeMemory: (postId: string) => {
        set((state) => {
          const newMemories = new Set(state.memories)
          newMemories.delete(postId)
          return { memories: newMemories }
        })
      },

      // Share management
      addShare: (postId: string) => {
        set((state) => {
          const newShares = new Set(state.shares)
          newShares.add(postId)
          
          const newHistory = [...state.interactionHistory, {
            postId,
            type: InteractionType.SHARE,
            timestamp: new Date(),
          }]
          
          return { 
            shares: newShares,
            interactionHistory: newHistory,
          }
        })
      },

      // Save management
      addSave: (postId: string) => {
        set((state) => {
          const newSaves = new Set(state.saves)
          newSaves.add(postId)
          return { saves: newSaves }
        })
      },

      removeSave: (postId: string) => {
        set((state) => {
          const newSaves = new Set(state.saves)
          newSaves.delete(postId)
          return { saves: newSaves }
        })
      },

      // View tracking
      incrementView: (postId: string) => {
        set((state) => {
          const newViews = new Map(state.views)
          const currentViews = newViews.get(postId) || 0
          newViews.set(postId, currentViews + 1)
          
          const newHistory = [...state.interactionHistory, {
            postId,
            type: InteractionType.VIEW,
            timestamp: new Date(),
          }]
          
          return { 
            views: newViews,
            interactionHistory: newHistory,
          }
        })
      },

      updateWatchTime: (postId: string, time: number) => {
        set((state) => {
          const newWatchTime = new Map(state.watchTime)
          newWatchTime.set(postId, time)
          return { watchTime: newWatchTime }
        })
      },

      // Loading state management
      setReactionLoading: (postId: string, loading: boolean) => {
        set((state) => {
          const newLoading = new Set(state.loadingReactions)
          if (loading) {
            newLoading.add(postId)
          } else {
            newLoading.delete(postId)
          }
          return { loadingReactions: newLoading }
        })
      },

      setMemoryLoading: (postId: string, loading: boolean) => {
        set((state) => {
          const newLoading = new Set(state.loadingMemories)
          if (loading) {
            newLoading.add(postId)
          } else {
            newLoading.delete(postId)
          }
          return { loadingMemories: newLoading }
        })
      },

      setShareLoading: (postId: string, loading: boolean) => {
        set((state) => {
          const newLoading = new Set(state.loadingShares)
          if (loading) {
            newLoading.add(postId)
          } else {
            newLoading.delete(postId)
          }
          return { loadingShares: newLoading }
        })
      },

      setSaveLoading: (postId: string, loading: boolean) => {
        set((state) => {
          const newLoading = new Set(state.loadingSaves)
          if (loading) {
            newLoading.add(postId)
          } else {
            newLoading.delete(postId)
          }
          return { loadingSaves: newLoading }
        })
      },

      // Bulk operations
      loadUserInteractions: (interactions: UserInteraction[]) => {
        set({ interactionHistory: interactions })
        
        // Process interactions to populate maps/sets
        const reactions = new Map<string, ReactionType>()
        const memories = new Set<string>()
        const shares = new Set<string>()
        const views = new Map<string, number>()
        
        interactions.forEach((interaction) => {
          switch (interaction.type) {
            case InteractionType.LIKE:
              if (interaction.reactionType) {
                reactions.set(interaction.postId, interaction.reactionType)
              }
              break
            case InteractionType.MEMORY:
              memories.add(interaction.postId)
              break
            case InteractionType.SHARE:
              shares.add(interaction.postId)
              break
            case InteractionType.VIEW:
              const currentViews = views.get(interaction.postId) || 0
              views.set(interaction.postId, currentViews + 1)
              break
          }
        })
        
        set({ reactions, memories, shares, views })
      },

      clearInteractions: () => {
        set({
          reactions: new Map(),
          memories: new Set(),
          shares: new Set(),
          saves: new Set(),
          views: new Map(),
          watchTime: new Map(),
          interactionHistory: [],
        })
      },

      // Analytics
      getEngagementScore: (postId: string) => {
        const { reactions, memories, shares, views, watchTime } = get()
        
        let score = 0
        if (reactions.has(postId)) score += 10
        if (memories.has(postId)) score += 20
        if (shares.has(postId)) score += 30
        score += (views.get(postId) || 0) * 1
        score += (watchTime.get(postId) || 0) * 0.1
        
        return score
      },

      getMostEngagedPosts: (limit = 10) => {
        const { interactionHistory } = get()
        const postScores = new Map<string, number>()
        
        interactionHistory.forEach((interaction) => {
          const currentScore = postScores.get(interaction.postId) || 0
          postScores.set(interaction.postId, currentScore + 1)
        })
        
        return Array.from(postScores.entries())
          .sort(([, a], [, b]) => b - a)
          .slice(0, limit)
          .map(([postId]) => postId)
      },
    }),
    {
      name: 'hvppy-interaction-store',
    }
  )
)

// Selectors for optimized re-renders
export const selectUserReaction = (postId: string) => (state: InteractionState) =>
  state.reactions.get(postId)

export const selectHasMemory = (postId: string) => (state: InteractionState) =>
  state.memories.has(postId)

export const selectHasShare = (postId: string) => (state: InteractionState) =>
  state.shares.has(postId)

export const selectHasSave = (postId: string) => (state: InteractionState) =>
  state.saves.has(postId)

export const selectIsReactionLoading = (postId: string) => (state: InteractionState) =>
  state.loadingReactions.has(postId)

export const selectIsMemoryLoading = (postId: string) => (state: InteractionState) =>
  state.loadingMemories.has(postId)

export const selectIsShareLoading = (postId: string) => (state: InteractionState) =>
  state.loadingShares.has(postId)

export const selectIsSaveLoading = (postId: string) => (state: InteractionState) =>
  state.loadingSaves.has(postId)

export const selectViewCount = (postId: string) => (state: InteractionState) =>
  state.views.get(postId) || 0

export const selectWatchTime = (postId: string) => (state: InteractionState) =>
  state.watchTime.get(postId) || 0
