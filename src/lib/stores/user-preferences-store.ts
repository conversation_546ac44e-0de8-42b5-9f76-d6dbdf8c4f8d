import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { FeedType } from '@/types/feed'
import { ContentType, ReactionType } from '@/types'

interface NotificationSettings {
  newFollowerContent: boolean
  moodMatches: boolean
  trending: boolean
  comments: boolean
  likes: boolean
  shares: boolean
  memories: boolean
  liveStreams: boolean
  personaUpdates: boolean
  emailNotifications: boolean
  pushNotifications: boolean
}

interface PrivacySettings {
  profileVisibility: 'public' | 'followers' | 'private'
  showActivity: boolean
  showFollowers: boolean
  showFollowing: boolean
  allowDirectMessages: 'everyone' | 'followers' | 'none'
  allowMentions: 'everyone' | 'followers' | 'none'
  showOnlineStatus: boolean
  dataCollection: boolean
  analyticsSharing: boolean
}

interface AccessibilitySettings {
  reducedMotion: boolean
  highContrast: boolean
  largeText: boolean
  screenReader: boolean
  autoplayVideos: boolean
  autoplayAudio: boolean
  showCaptions: boolean
  hapticFeedback: boolean
}

interface ContentPreferences {
  preferredMoods: string[]
  blockedMoods: string[]
  preferredContentTypes: ContentType[]
  blockedContentTypes: ContentType[]
  blockedCreators: string[]
  blockedKeywords: string[]
  explicitContent: boolean
  experimentalFeatures: boolean
  aiRecommendations: boolean
}

interface UserPreferencesState {
  // Core preferences
  defaultFeedType: FeedType
  language: string
  timezone: string
  theme: 'light' | 'dark' | 'auto'
  
  // Settings
  notifications: NotificationSettings
  privacy: PrivacySettings
  accessibility: AccessibilitySettings
  content: ContentPreferences
  
  // Feed preferences
  autoPlay: boolean
  muteByDefault: boolean
  preferredQuality: 'low' | 'medium' | 'high' | 'auto'
  preloadDistance: number
  offloadDistance: number
  
  // Actions
  setDefaultFeedType: (feedType: FeedType) => void
  setLanguage: (language: string) => void
  setTimezone: (timezone: string) => void
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  updateNotifications: (settings: Partial<NotificationSettings>) => void
  updatePrivacy: (settings: Partial<PrivacySettings>) => void
  updateAccessibility: (settings: Partial<AccessibilitySettings>) => void
  updateContent: (settings: Partial<ContentPreferences>) => void
  setAutoPlay: (autoPlay: boolean) => void
  setMuteByDefault: (mute: boolean) => void
  setPreferredQuality: (quality: 'low' | 'medium' | 'high' | 'auto') => void
  setPreloadDistance: (distance: number) => void
  setOffloadDistance: (distance: number) => void
  addPreferredMood: (mood: string) => void
  removePreferredMood: (mood: string) => void
  addBlockedCreator: (creatorId: string) => void
  removeBlockedCreator: (creatorId: string) => void
  addBlockedKeyword: (keyword: string) => void
  removeBlockedKeyword: (keyword: string) => void
  resetToDefaults: () => void
}

const defaultNotifications: NotificationSettings = {
  newFollowerContent: true,
  moodMatches: true,
  trending: false,
  comments: true,
  likes: true,
  shares: true,
  memories: true,
  liveStreams: true,
  personaUpdates: true,
  emailNotifications: false,
  pushNotifications: true,
}

const defaultPrivacy: PrivacySettings = {
  profileVisibility: 'public',
  showActivity: true,
  showFollowers: true,
  showFollowing: true,
  allowDirectMessages: 'followers',
  allowMentions: 'everyone',
  showOnlineStatus: true,
  dataCollection: true,
  analyticsSharing: false,
}

const defaultAccessibility: AccessibilitySettings = {
  reducedMotion: false,
  highContrast: false,
  largeText: false,
  screenReader: false,
  autoplayVideos: true,
  autoplayAudio: false,
  showCaptions: false,
  hapticFeedback: true,
}

const defaultContent: ContentPreferences = {
  preferredMoods: [],
  blockedMoods: [],
  preferredContentTypes: [],
  blockedContentTypes: [],
  blockedCreators: [],
  blockedKeywords: [],
  explicitContent: false,
  experimentalFeatures: false,
  aiRecommendations: true,
}

export const useUserPreferencesStore = create<UserPreferencesState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        defaultFeedType: FeedType.DISCOVER,
        language: 'en',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        theme: 'auto',
        notifications: defaultNotifications,
        privacy: defaultPrivacy,
        accessibility: defaultAccessibility,
        content: defaultContent,
        autoPlay: true,
        muteByDefault: true,
        preferredQuality: 'auto',
        preloadDistance: 3,
        offloadDistance: 10,

        // Actions
        setDefaultFeedType: (feedType: FeedType) => {
          set({ defaultFeedType: feedType })
        },

        setLanguage: (language: string) => {
          set({ language })
        },

        setTimezone: (timezone: string) => {
          set({ timezone })
        },

        setTheme: (theme: 'light' | 'dark' | 'auto') => {
          set({ theme })
        },

        updateNotifications: (settings: Partial<NotificationSettings>) => {
          set((state) => ({
            notifications: { ...state.notifications, ...settings },
          }))
        },

        updatePrivacy: (settings: Partial<PrivacySettings>) => {
          set((state) => ({
            privacy: { ...state.privacy, ...settings },
          }))
        },

        updateAccessibility: (settings: Partial<AccessibilitySettings>) => {
          set((state) => ({
            accessibility: { ...state.accessibility, ...settings },
          }))
        },

        updateContent: (settings: Partial<ContentPreferences>) => {
          set((state) => ({
            content: { ...state.content, ...settings },
          }))
        },

        setAutoPlay: (autoPlay: boolean) => {
          set({ autoPlay })
        },

        setMuteByDefault: (mute: boolean) => {
          set({ muteByDefault: mute })
        },

        setPreferredQuality: (quality: 'low' | 'medium' | 'high' | 'auto') => {
          set({ preferredQuality: quality })
        },

        setPreloadDistance: (distance: number) => {
          set({ preloadDistance: distance })
        },

        setOffloadDistance: (distance: number) => {
          set({ offloadDistance: distance })
        },

        addPreferredMood: (mood: string) => {
          set((state) => ({
            content: {
              ...state.content,
              preferredMoods: [...state.content.preferredMoods, mood],
            },
          }))
        },

        removePreferredMood: (mood: string) => {
          set((state) => ({
            content: {
              ...state.content,
              preferredMoods: state.content.preferredMoods.filter((m) => m !== mood),
            },
          }))
        },

        addBlockedCreator: (creatorId: string) => {
          set((state) => ({
            content: {
              ...state.content,
              blockedCreators: [...state.content.blockedCreators, creatorId],
            },
          }))
        },

        removeBlockedCreator: (creatorId: string) => {
          set((state) => ({
            content: {
              ...state.content,
              blockedCreators: state.content.blockedCreators.filter((id) => id !== creatorId),
            },
          }))
        },

        addBlockedKeyword: (keyword: string) => {
          set((state) => ({
            content: {
              ...state.content,
              blockedKeywords: [...state.content.blockedKeywords, keyword],
            },
          }))
        },

        removeBlockedKeyword: (keyword: string) => {
          set((state) => ({
            content: {
              ...state.content,
              blockedKeywords: state.content.blockedKeywords.filter((k) => k !== keyword),
            },
          }))
        },

        resetToDefaults: () => {
          set({
            defaultFeedType: FeedType.DISCOVER,
            language: 'en',
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            theme: 'auto',
            notifications: defaultNotifications,
            privacy: defaultPrivacy,
            accessibility: defaultAccessibility,
            content: defaultContent,
            autoPlay: true,
            muteByDefault: true,
            preferredQuality: 'auto',
            preloadDistance: 3,
            offloadDistance: 10,
          })
        },
      }),
      {
        name: 'hvppy-user-preferences-store',
      }
    ),
    {
      name: 'hvppy-user-preferences-store',
    }
  )
)

// Selectors for optimized re-renders
export const selectNotifications = (state: UserPreferencesState) => state.notifications
export const selectPrivacy = (state: UserPreferencesState) => state.privacy
export const selectAccessibility = (state: UserPreferencesState) => state.accessibility
export const selectContent = (state: UserPreferencesState) => state.content
export const selectTheme = (state: UserPreferencesState) => state.theme
export const selectLanguage = (state: UserPreferencesState) => state.language
export const selectAutoPlay = (state: UserPreferencesState) => state.autoPlay
export const selectMuteByDefault = (state: UserPreferencesState) => state.muteByDefault
export const selectPreferredQuality = (state: UserPreferencesState) => state.preferredQuality
