import { FeedItem } from '@/types/feed'
import { ContentType } from '@/types'

// HVPPY Content Classification Types
interface HVPPYContentItem {
  id: string
  title: string
  filename: string
  contentType: string
  moods: string[]
  youtubeId?: string
  featuring?: string[]
  relatedAudio?: string
  relatedVideo?: string
  format?: string
  series?: string
  episode?: number
  category?: string
  location?: string
}

interface HVPPYContentClassification {
  metadata: {
    artist: string
    genres: string[]
    primaryLocation: string
  }
  featured: HVPPYContentItem
  categories: {
    music_videos: { items: HVPPYContentItem[] }
    visualizers: { items: HVPPYContentItem[] }
    documentary: { items: HVPPYContentItem[] }
    promotional: { items: HVPPYContentItem[] }
    interviews: { items: HVPPYContentItem[] }
    audio: { items: HVPPYContentItem[] }
  }
  moods: Record<string, { description: string; color: string; count: number }>
}

// Content type mapping
const mapContentType = (hvppyType: string): ContentType => {
  switch (hvppyType) {
    case 'music_video':
    case 'visualizer':
    case 'documentary':
    case 'promotional':
    case 'interview':
    case 'podcast':
      return ContentType.VIDEO
    case 'audio':
    case 'radio_interview':
      return ContentType.AUDIO
    default:
      return ContentType.VIDEO
  }
}

// Convert HVPPY content item to FeedItem
const convertToFeedItem = (hvppyItem: HVPPYContentItem, index: number): FeedItem => {
  const contentType = mapContentType(hvppyItem.contentType)
  const mediaUrl = `/hvppy-content/${hvppyItem.filename}`
  
  // Generate thumbnail URL (for videos, we'll use a placeholder or extract frame)
  const thumbnailUrl = contentType === ContentType.VIDEO 
    ? `/hvppy-content/thumbnails/${hvppyItem.id}.jpg` // We'll need to generate these
    : '/images/audio-placeholder.jpg'

  return {
    id: hvppyItem.id,
    post: {
      id: hvppyItem.id,
      userId: 'hvppy-artist',
      title: hvppyItem.title,
      content: generateContentDescription(hvppyItem),
      contentType,
      contentUrl: mediaUrl,
      mediaUrls: [mediaUrl],
      thumbnailUrl,
      transcodingStatus: 'COMPLETED' as const,
      moderationStatus: 'APPROVED' as const,
      moods: hvppyItem.moods,
      viewCount: Math.floor(Math.random() * 10000) + 1000, // Simulated view count
      likeCount: Math.floor(Math.random() * 1000) + 100,
      shareCount: Math.floor(Math.random() * 100) + 10,
      reactions: [],
      memories: [],
      comments: [],
      isPublic: true,
      isExperimental: false,
      createdAt: new Date(Date.now() - index * 24 * 60 * 60 * 1000), // Stagger dates
      updatedAt: new Date(Date.now() - index * 12 * 60 * 60 * 1000),
      publishedAt: new Date(Date.now() - index * 24 * 60 * 60 * 1000),
      user: {
        id: 'hvppy-artist',
        appwriteId: 'hvppy-artist-appwrite',
        email: '<EMAIL>',
        username: 'hvppy',
        displayName: 'HVPPY',
        avatarUrl: '/images/hvppy-avatar.jpg',
        role: 'artist',
        isVerified: true,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date(),
      },
    },
    metadata: {
      algorithm: 'content_based',
      score: 0.9 + Math.random() * 0.1, // High score for featured content
      source: 'hvppy_content',
      youtubeId: hvppyItem.youtubeId,
      featuring: hvppyItem.featuring,
      category: hvppyItem.contentType,
    },
  }
}

// Generate content description based on item type and metadata
const generateContentDescription = (item: HVPPYContentItem): string => {
  const baseDescription = `${item.title} by HVPPY`
  
  if (item.featuring && item.featuring.length > 0) {
    return `${baseDescription} featuring ${item.featuring.join(', ')}`
  }
  
  if (item.series && item.episode) {
    return `${baseDescription} - ${item.series} Episode ${item.episode}`
  }
  
  if (item.category) {
    return `${baseDescription} - ${item.category.replace('_', ' ')}`
  }
  
  return baseDescription
}

// Load and parse HVPPY content classification
export const loadHVPPYContent = async (): Promise<FeedItem[]> => {
  try {
    const response = await fetch('/hvppy-content/content-classification.json')
    if (!response.ok) {
      throw new Error('Failed to load HVPPY content classification')
    }
    
    const classification: HVPPYContentClassification = await response.json()
    const feedItems: FeedItem[] = []
    let index = 0
    
    // Add featured content first
    feedItems.push(convertToFeedItem(classification.featured, index++))
    
    // Add content from all categories
    const categories = [
      classification.categories.music_videos.items,
      classification.categories.visualizers.items,
      classification.categories.documentary.items,
      classification.categories.promotional.items,
      classification.categories.interviews.items,
      // Note: Audio items might need special handling for pure audio content
      classification.categories.audio.items.slice(0, 5), // Limit audio items
    ]
    
    categories.forEach(categoryItems => {
      categoryItems.forEach(item => {
        // Skip if already added (featured content might be in categories too)
        if (!feedItems.find(feedItem => feedItem.id === item.id)) {
          feedItems.push(convertToFeedItem(item, index++))
        }
      })
    })
    
    return feedItems
  } catch (error) {
    console.error('Error loading HVPPY content:', error)
    return []
  }
}

// Filter content by mood
export const filterContentByMood = (content: FeedItem[], selectedMoods: string[]): FeedItem[] => {
  if (selectedMoods.length === 0) return content
  
  return content.filter(item => 
    item.post.moods.some(mood => selectedMoods.includes(mood))
  )
}

// Get available moods from content
export const getAvailableMoods = async (): Promise<string[]> => {
  try {
    const response = await fetch('/hvppy-content/content-classification.json')
    if (!response.ok) return []
    
    const classification: HVPPYContentClassification = await response.json()
    return Object.keys(classification.moods)
  } catch (error) {
    console.error('Error loading moods:', error)
    return []
  }
}
