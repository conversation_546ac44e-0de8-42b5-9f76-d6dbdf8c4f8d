// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                     String    @id @default(cuid())
  name                   String?
  email                  String?   @unique
  emailVerified          DateTime?
  image                  String?
  password               String? // Added for credentials provider
  twoFactorEnabled       Boolean   @default(false)
  twoFactorSecret        String?   @db.Text
  twoFactorRecoveryCodes String[]  // Array of strings for recovery codes
  
  // Custom fields for HVPPY Central
  appwriteId             String?   @unique // Links to Appwrite user ID
  username               String?   @unique
  displayName            String?
  bio                    String?
  avatar                 String?
  role                   UserRole  @default(FAN)
  isVerified             Boolean   @default(false)

  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt

  accounts               Account[]
  sessions               Session[]
  resetTokens            ResetToken[] // Added for password reset
  likes                  Like[] // User can like many posts
  comments               Comment[] // User can make many comments
  saves                  Save[] // User can save many posts
  notifications          Notification[] // User can receive many notifications
  sentNotifications      Notification[] @relation("NotificationFromUser") // User can send notifications
  
  // Relations from original schema
  creator                Creator?
  posts                  Post[]
  reactions              Reaction[]
  memories               Memory[]
  followers              Follow[]  @relation("UserFollowers")
  following              Follow[]  @relation("UserFollowing")

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model ResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("reset_tokens")
}

// Creator profile - for content creators
model Creator {
  id          String  @id @default(cuid())
  userId      String  @unique
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Creator-specific fields
  stageName   String?
  genre       String[]
  location    String?
  website     String?
  socialLinks Json?
  
  // Analytics
  totalFollowers Int @default(0)
  totalViews     Int @default(0)
  totalLikes     Int @default(0)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  personas Persona[]
  posts    Post[]
  
  @@map("creators")
}

// Persona model - multiple identities per creator
model Persona {
  id          String  @id @default(cuid())
  creatorId   String
  creator     Creator @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  
  name        String
  description String?
  avatar      String?
  coverImage  String?
  mood        String[] // Associated moods
  isActive    Boolean @default(true)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  posts Post[]
  
  @@map("personas")
}

// Post model - main content posts
model Post {
  id               String          @id @default(cuid())
  userId           String
  user             User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  creatorId        String?
  creator          Creator?        @relation(fields: [creatorId], references: [id], onDelete: SetNull)
  personaId        String?
  persona          Persona?        @relation(fields: [personaId], references: [id], onDelete: SetNull)
  
  // Content
  title            String?
  content          String
  contentType      ContentType
  contentUrl       String? // Reference to HVPPY content ID or external content
  mediaUrls        String[]
  thumbnailUrl     String?
  
  // Transcoding
  transcodingStatus TranscodingStatus @default(PENDING)
  renditions       Json? // Store URLs/metadata of different transcoded versions

  // Moderation
  moderationStatus ModerationStatus @default(PENDING)
  moderationNotes  String? // Notes from moderation

  // Emotional AI
  moods            String[]
  aiAnalysis       Json? // AI-generated mood and content analysis
  
  // Engagement
  viewCount        Int             @default(0)
  likeCount        Int             @default(0)
  shareCount       Int             @default(0)
  likes            Like[] // Post can have many likes
  comments         Comment[] // Post can have many comments
  saves            Save[] // Post can be saved by many users
  notifications    Notification[] // Post can trigger notifications
  
  // Engagement Hub
  creatorNotes     String?
  supporterActions Json?

  // Visibility
  isPublic         Boolean         @default(true)
  isExperimental   Boolean         @default(false) // For experimental content lab
  
  // Timestamps
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  publishedAt      DateTime?
  
  // Relations
  reactions        Reaction[]
  memories         Memory[]
  
  @@map("posts")
}

// Like model
model Like {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, postId]) // A user can only like a post once
  @@map("likes")
}

// Comment model
model Comment {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  content   String
  createdAt DateTime @default(now())

  @@map("comments")
}

// Save model - saved posts
model Save {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, postId]) // A user can only save a post once
  @@map("saves")
}

// Reaction model - likes, loves, etc.
model Reaction {
  id     String      @id @default(cuid())
  userId String
  user   User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId String
  post   Post        @relation(fields: [postId], references: [id], onDelete: Cascade)
  
  type   ReactionType
  mood   String? // User's mood when reacting
  
  createdAt DateTime @default(now())
  
  @@unique([userId, postId])
  @@map("reactions")
}

// Memory model - fan-saved moments
model Memory {
  id          String @id @default(cuid())
  userId      String
  user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId      String
  post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  
  title       String?
  description String?
  timestamp   Float? // Timestamp in media for video/audio clips
  thumbnailUrl String?
  
  createdAt DateTime @default(now())
  
  @@unique([userId, postId])
  @@map("memories")
}

// Notification model - user notifications
model Notification {
  id        String @id @default(cuid())
  userId    String
  user      User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  fromUserId String?
  fromUser  User?  @relation("NotificationFromUser", fields: [fromUserId], references: [id], onDelete: Cascade)
  postId    String?
  post      Post?  @relation(fields: [postId], references: [id], onDelete: Cascade)

  type      String // 'like', 'comment', 'follow', 'mention', 'share', 'memory', 'mood_match'
  message   String
  isRead    Boolean @default(false)
  metadata  Json?   // Additional data specific to notification type

  createdAt DateTime @default(now())

  @@map("notifications")
}

// Follow model - user relationships
model Follow {
  id          String @id @default(cuid())
  followerId  String
  follower    User   @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)
  followingId String
  following   User   @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([followerId, followingId])
  @@map("follows")
}

// Enums
enum UserRole {
  FAN
  CREATOR
  ADMIN
}

enum ContentType {
  MUSIC
  VIDEO
  IMAGE
  TEXT
  STORY
  LIVE
  MEMORY
}

enum ReactionType {
  LIKE
  LOVE
  FIRE
  MIND_BLOWN
  VIBE
  MOOD_MATCH
}

enum TranscodingStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum ModerationStatus {
  PENDING
  APPROVED
  REJECTED
}