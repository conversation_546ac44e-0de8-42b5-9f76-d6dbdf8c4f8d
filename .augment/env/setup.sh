#!/bin/bash

# Create mobile app package.json
cat > apps/mobile/package.json << 'EOF'
{
  "name": "@webrtc-audio/mobile",
  "version": "1.0.0",
  "description": "React Native mobile app for WebRTC audio capture",
  "main": "index.js",
  "scripts": {
    "start": "expo start",
    "dev": "expo start",
    "build": "expo build",
    "test": "jest",
    "lint": "eslint .",
    "clean": "rm -rf node_modules dist"
  },
  "dependencies": {
    "expo": "~51.0.0",
    "react": "18.2.0",
    "react-native": "0.74.5"
  },
  "devDependencies": {
    "@babel/core": "^7.20.0",
    "jest": "^29.0.0"
  }
}
EOF

# Create desktop app package.json
cat > apps/desktop/package.json << 'EOF'
{
  "name": "@webrtc-audio/desktop",
  "version": "1.0.0",
  "description": "Electron desktop app for WebRTC audio reception",
  "main": "src/main.js",
  "scripts": {
    "start": "electron .",
    "dev": "electron . --dev",
    "build": "electron-builder",
    "test": "jest",
    "lint": "eslint .",
    "clean": "rm -rf node_modules dist"
  },
  "dependencies": {
    "electron": "^32.0.0"
  },
  "devDependencies": {
    "electron-builder": "^25.0.0",
    "jest": "^29.0.0"
  }
}
EOF

# Create signaling server package.json
cat > apps/signaling-server/package.json << 'EOF'
{
  "name": "@webrtc-audio/signaling-server",
  "version": "1.0.0",
  "description": "Node.js WebSocket signaling server for WebRTC",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "build": "echo 'No build needed for Node.js'",
    "test": "jest",
    "lint": "eslint .",
    "clean": "rm -rf node_modules"
  },
  "dependencies": {
    "ws": "^8.0.0",
    "express": "^4.18.0"
  },
  "devDependencies": {
    "nodemon": "^3.0.0",
    "jest": "^29.0.0"
  }
}
EOF

# Create shared package package.json
cat > packages/shared/package.json << 'EOF'
{
  "name": "@webrtc-audio/shared",
  "version": "1.0.0",
  "description": "Shared utilities and types for WebRTC audio streaming",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "test": "jest",
    "lint": "eslint .",
    "clean": "rm -rf dist node_modules"
  },
  "dependencies": {},
  "devDependencies": {
    "typescript": "^5.0.0",
    "jest": "^29.0.0"
  }
}
EOF

# Create webrtc-core package package.json
cat > packages/webrtc-core/package.json << 'EOF'
{
  "name": "@webrtc-audio/webrtc-core",
  "version": "1.0.0",
  "description": "WebRTC abstraction layer and utilities",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "test": "jest",
    "lint": "eslint .",
    "clean": "rm -rf dist node_modules"
  },
  "dependencies": {
    "@webrtc-audio/shared": "workspace:*"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "jest": "^29.0.0"
  }
}
EOF

# Create eslint-config package package.json
cat > packages/eslint-config/package.json << 'EOF'
{
  "name": "@webrtc-audio/eslint-config",
  "version": "1.0.0",
  "description": "Shared ESLint configuration",
  "main": "index.js",
  "scripts": {
    "test": "echo 'No tests for config package'",
    "lint": "echo 'No linting for config package'",
    "build": "echo 'No build needed for config package'",
    "clean": "rm -rf node_modules"
  },
  "dependencies": {
    "eslint": "^8.0.0"
  }
}
EOF

echo "Package.json files created for all apps and packages!"
find . -name "package.json" -type f